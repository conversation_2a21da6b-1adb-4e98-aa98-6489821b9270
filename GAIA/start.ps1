# PowerShell script to start both backend and frontend servers

Write-Host "Starting GAIA - GPT AI Assistant..." -ForegroundColor Green
Write-Host "----------------------------------" -ForegroundColor Green

# Start the backend server
Write-Host "Starting backend server..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PSScriptRoot\backend'; python run.py"

# Wait a moment for the backend to initialize
Start-Sleep -Seconds 2

# Start the frontend server
Write-Host "Starting frontend server..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd '$PSScriptRoot'; npm start"

Write-Host "Both servers are now starting in separate windows!" -ForegroundColor Green
Write-Host "Frontend will be available at: http://localhost:3000" -ForegroundColor Cyan
Write-Host "Backend API will be available at: http://localhost:8000" -ForegroundColor Cyan 