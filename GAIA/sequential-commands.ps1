# PowerShell sequential commands script
# This script runs commands in sequence for Windows PowerShell

Write-Host "Starting servers separately in sequence..." -ForegroundColor Green
Write-Host "---------------------------------------------" -ForegroundColor Green

# First, start the backend
Write-Host "1. Starting backend server..." -ForegroundColor Yellow
Write-Host "   Navigate to: gaia-assistant\backend" -ForegroundColor Cyan

Set-Location -Path ".\gaia-assistant\backend"
Write-Host "   Current directory: $PWD" -ForegroundColor Cyan
Write-Host "   Command: python run.py" -ForegroundColor Cyan
Write-Host "   (Start the backend server in a separate PowerShell window)" -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "python run.py"

# Wait a moment to ensure backend starts properly
Start-Sleep -Seconds 3

# Then, start the frontend
Write-Host "2. Starting frontend server..." -ForegroundColor Yellow
Write-Host "   Navigate to: gaia-assistant" -ForegroundColor Cyan

Set-Location -Path ".."
Write-Host "   Current directory: $PWD" -ForegroundColor Cyan
Write-Host "   Command: npm start" -ForegroundColor Cyan
Write-Host "   (Start the frontend server in a separate PowerShell window)" -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm start"

Write-Host "Both servers should now be starting!" -ForegroundColor Green
Write-Host "Frontend will be available at: http://localhost:3000" -ForegroundColor Cyan
Write-Host "Backend API will be available at: http://localhost:8000" -ForegroundColor Cyan

# Return to original directory
Set-Location -Path ".."
Write-Host "Returned to: $PWD" -ForegroundColor Cyan 