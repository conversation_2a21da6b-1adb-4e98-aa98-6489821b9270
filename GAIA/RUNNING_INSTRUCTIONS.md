# Running GAIA - GPT AI Assistant

This document provides instructions for running the GAIA application and troubleshooting common issues.

## Prerequisites

- Node.js (v16+)
- Python (v3.10+)
- OpenAI API key
- PostgreSQL with pgvector extension
- Google Cloud project with OAuth credentials

## Starting the Application

### Using PowerShell Scripts

We've provided several PowerShell scripts to make running the application easier:

1. **Start both servers (recommended)**:
   ```
   .\start.ps1
   ```
   This will open two PowerShell windows, one for the backend and one for the frontend.

2. **Start just the backend**:
   ```
   .\start-backend.ps1
   ```

3. **Start just the frontend**:
   ```
   .\start-frontend.ps1
   ```

### Manual Start

If you prefer to start the servers manually:

1. **Backend**:
   ```powershell
   cd backend
   python run.py
   ```

2. **Frontend**:
   ```powershell
   npm start
   ```

## Google OAuth Configuration

If you encounter OAuth errors when connecting to Google, follow these steps:

1. **Check your Google Cloud Console configuration**:
   - Go to https://console.cloud.google.com
   - Navigate to your project
   - Go to "APIs & Services" > "Credentials"
   - Edit your OAuth 2.0 Client ID

2. **Verify redirect URI**:
   - Ensure your redirect URI matches exactly what's in your .env file
   - It should be: `http://localhost:8000/api/auth/google/callback`

3. **Check authorized domains**:
   - Add `localhost` to your authorized domains list

4. **Update your .env file**:
   ```
   GOOGLE_CLIENT_ID=your_client_id_here
   GOOGLE_CLIENT_SECRET=your_client_secret_here
   OAUTH_REDIRECT_URI=http://localhost:8000/api/auth/google/callback
   ```

5. **Enable necessary APIs**:
   - Google Drive API
   - Google OAuth API

## Troubleshooting

### Authentication Errors

- **"Error 400: invalid_request"**: This typically happens when there's an issue with the state parameter. Make sure you're not passing the state parameter twice in the OAuth URL.

- **"Error 401: invalid_client"**: Check that your client ID and client secret are correct in the .env file.

- **"Error: redirect_uri_mismatch"**: Ensure the redirect URI in your Google Cloud Console exactly matches the one in your application.

### Database Connection Issues

- Make sure PostgreSQL is running
- Verify that pgvector extension is installed
- Check your DATABASE_URL in the .env file

### API Key Issues

- Ensure your OpenAI API key is correct and has not expired
- Check that it has permissions for embeddings and completions

## Getting Help

If you encounter issues not covered in this guide, please:

1. Check the error logs in both the backend and frontend terminals
2. Look for specific error messages that can help diagnose the problem
3. Refer to the README.md for additional information about the application architecture 