#!/usr/bin/env python3
"""
Enhanced RAG System Setup Script for GAIA

This script sets up the complete enhanced RAG system including:
- PostgreSQL database with pgvector
- Redis for background tasks
- Environment validation
- Database initialization
- Index optimization
"""

import os
import sys
import asyncio
import logging
import subprocess
from pathlib import Path
from typing import Dict, Any, List
import psycopg
import redis
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedRagSetup:
    """Setup manager for the enhanced RAG system"""
    
    def __init__(self):
        """Initialize the setup manager"""
        self.required_env_vars = [
            'OPENAI_API_KEY',
            'ANTHROPIC_API_KEY',
            'DATABASE_URL',
            'REDIS_URL'
        ]
        self.optional_env_vars = [
            'GOOGLE_CLIENT_ID',
            'GOOGLE_CLIENT_SECRET',
            'GOOGLE_REDIRECT_URI'
        ]
        
    def check_environment(self) -> bool:
        """Check if all required environment variables are set"""
        logger.info("Checking environment variables...")
        
        missing_vars = []
        for var in self.required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
            logger.info("Please copy .env.example to .env and fill in the required values")
            return False
        
        # Check optional variables
        missing_optional = []
        for var in self.optional_env_vars:
            if not os.getenv(var):
                missing_optional.append(var)
        
        if missing_optional:
            logger.warning(f"Missing optional environment variables: {', '.join(missing_optional)}")
            logger.warning("Google Drive integration will not be available")
        
        logger.info("Environment variables check completed")
        return True
    
    def check_dependencies(self) -> bool:
        """Check if all required dependencies are installed"""
        logger.info("Checking Python dependencies...")
        
        required_packages = [
            'fastapi',
            'uvicorn',
            'asyncpg',
            'psycopg',
            'openai',
            'anthropic',
            'celery',
            'redis',
            'tiktoken',
            'google-auth',
            'google-api-python-client'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            logger.error(f"Missing required packages: {', '.join(missing_packages)}")
            logger.info("Please run: pip install -r requirements.txt")
            return False
        
        logger.info("All required dependencies are installed")
        return True
    
    def check_postgresql(self) -> bool:
        """Check PostgreSQL connection and pgvector extension"""
        logger.info("Checking PostgreSQL connection...")
        
        try:
            database_url = os.getenv('DATABASE_URL')
            conn = psycopg.connect(database_url)
            
            with conn.cursor() as cur:
                # Check if pgvector extension is available
                cur.execute("SELECT 1 FROM pg_available_extensions WHERE name = 'vector'")
                if not cur.fetchone():
                    logger.error("pgvector extension is not available")
                    logger.info("Please install pgvector: https://github.com/pgvector/pgvector")
                    return False
                
                # Check if extension is installed
                cur.execute("SELECT 1 FROM pg_extension WHERE extname = 'vector'")
                if not cur.fetchone():
                    logger.info("Installing pgvector extension...")
                    cur.execute("CREATE EXTENSION IF NOT EXISTS vector")
                    conn.commit()
                    logger.info("pgvector extension installed successfully")
                
                # Test vector operations
                cur.execute("SELECT '[1,2,3]'::vector")
                logger.info("Vector operations test passed")
            
            conn.close()
            logger.info("PostgreSQL connection successful")
            return True
            
        except Exception as e:
            logger.error(f"PostgreSQL connection failed: {str(e)}")
            logger.info("Please ensure PostgreSQL is running and DATABASE_URL is correct")
            return False
    
    def check_redis(self) -> bool:
        """Check Redis connection"""
        logger.info("Checking Redis connection...")
        
        try:
            redis_url = os.getenv('REDIS_URL')
            r = redis.Redis.from_url(redis_url)
            r.ping()
            logger.info("Redis connection successful")
            return True
            
        except Exception as e:
            logger.error(f"Redis connection failed: {str(e)}")
            logger.info("Please ensure Redis is running and REDIS_URL is correct")
            return False
    
    async def initialize_database(self) -> bool:
        """Initialize the database with required tables and indexes"""
        logger.info("Initializing database...")
        
        try:
            # Import and run database initialization
            from app.utils.db_utils import init_db
            await init_db()
            logger.info("Database initialization completed")
            return True
            
        except Exception as e:
            logger.error(f"Database initialization failed: {str(e)}")
            return False
    
    def create_directories(self) -> bool:
        """Create required directories"""
        logger.info("Creating required directories...")
        
        directories = [
            'logs',
            'uploads',
            'temp'
        ]
        
        try:
            for directory in directories:
                Path(directory).mkdir(exist_ok=True)
                logger.info(f"Created directory: {directory}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to create directories: {str(e)}")
            return False
    
    def test_api_keys(self) -> bool:
        """Test API key validity"""
        logger.info("Testing API keys...")
        
        # Test OpenAI API key
        try:
            import openai
            client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
            # Test with a simple embedding request
            response = client.embeddings.create(
                model="text-embedding-3-small",
                input="test"
            )
            logger.info("OpenAI API key is valid")
        except Exception as e:
            logger.error(f"OpenAI API key test failed: {str(e)}")
            return False
        
        # Test Anthropic API key
        try:
            import anthropic
            client = anthropic.Anthropic(api_key=os.getenv('ANTHROPIC_API_KEY'))
            # Test with a simple message
            response = client.messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=10,
                messages=[{"role": "user", "content": "Hello"}]
            )
            logger.info("Anthropic API key is valid")
        except Exception as e:
            logger.error(f"Anthropic API key test failed: {str(e)}")
            return False
        
        return True
    
    def setup_celery(self) -> bool:
        """Setup Celery for background tasks"""
        logger.info("Setting up Celery...")
        
        try:
            # Test Celery configuration
            from app.services.background_tasks import celery_app
            
            # Check if Celery can connect to Redis
            inspect = celery_app.control.inspect()
            stats = inspect.stats()
            
            if stats is None:
                logger.warning("Celery worker is not running")
                logger.info("To start Celery worker, run: celery -A app.services.background_tasks worker --loglevel=info")
                logger.info("To start Celery beat (scheduler), run: celery -A app.services.background_tasks beat --loglevel=info")
            else:
                logger.info("Celery is configured and workers are running")
            
            return True
            
        except Exception as e:
            logger.error(f"Celery setup failed: {str(e)}")
            return False
    
    async def run_setup(self) -> bool:
        """Run the complete setup process"""
        logger.info("Starting Enhanced RAG System Setup...")
        
        setup_steps = [
            ("Environment Variables", self.check_environment),
            ("Python Dependencies", self.check_dependencies),
            ("PostgreSQL & pgvector", self.check_postgresql),
            ("Redis", self.check_redis),
            ("Directories", self.create_directories),
            ("API Keys", self.test_api_keys),
            ("Database Initialization", self.initialize_database),
            ("Celery Setup", self.setup_celery),
        ]
        
        failed_steps = []
        
        for step_name, step_func in setup_steps:
            logger.info(f"\n{'='*50}")
            logger.info(f"Step: {step_name}")
            logger.info(f"{'='*50}")
            
            try:
                if asyncio.iscoroutinefunction(step_func):
                    success = await step_func()
                else:
                    success = step_func()
                
                if success:
                    logger.info(f"✅ {step_name} - SUCCESS")
                else:
                    logger.error(f"❌ {step_name} - FAILED")
                    failed_steps.append(step_name)
                    
            except Exception as e:
                logger.error(f"❌ {step_name} - ERROR: {str(e)}")
                failed_steps.append(step_name)
        
        logger.info(f"\n{'='*50}")
        logger.info("Setup Summary")
        logger.info(f"{'='*50}")
        
        if failed_steps:
            logger.error(f"Setup completed with {len(failed_steps)} failed steps:")
            for step in failed_steps:
                logger.error(f"  - {step}")
            logger.error("\nPlease fix the failed steps and run setup again")
            return False
        else:
            logger.info("🎉 Enhanced RAG System setup completed successfully!")
            logger.info("\nNext steps:")
            logger.info("1. Start the FastAPI server: python run.py")
            logger.info("2. Start Celery worker: celery -A app.services.background_tasks worker --loglevel=info")
            logger.info("3. Start Celery beat: celery -A app.services.background_tasks beat --loglevel=info")
            logger.info("4. Access the API at: http://localhost:8000")
            logger.info("5. View API docs at: http://localhost:8000/docs")
            return True

def main():
    """Main setup function"""
    # Load environment variables
    load_dotenv()
    
    # Change to backend directory if not already there
    if not Path('app').exists():
        backend_dir = Path(__file__).parent
        os.chdir(backend_dir)
    
    # Run setup
    setup = EnhancedRagSetup()
    success = asyncio.run(setup.run_setup())
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
