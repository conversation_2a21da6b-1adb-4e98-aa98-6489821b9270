# GAIA Google Drive Integration

This document outlines the implementation of Google Drive integration in GAIA, including the ingestion pipeline and RAG (Retrieval-Augmented Generation) system.

## Overview

We've implemented a LangChain-inspired Google Drive integration for GAIA with the following components:

1. **GoogleDriveLoader**: For fetching, downloading, and processing files from Google Drive
2. **RagService**: For handling embeddings and vector storage/retrieval in PostgreSQL with pgvector
3. **API Endpoints**: Clean endpoints for Drive sync, RAG ingest, and RAG query operations
4. **UI Integration**: Frontend toggle and sync functionality in the RightSidebar

## Backend Architecture

### Services

- **`drive_loader.py`**: GoogleDriveLoader class for fetching and processing Google Drive files
- **`rag_service.py`**: RagService class for embeddings and vector storage/retrieval

### Endpoints

- **`/api/drive/sync`**: Sync Google Drive files with RAG system
- **`/api/drive/status/{project_id}`**: Get Drive sync status for a project
- **`/api/rag/ingest`**: Ingest text chunks into RAG system
- **`/api/rag/query`**: Query for semantically similar chunks
- **`/api/rag/status/{project_id}`**: Get RAG status for a project
- **`/api/rag/project/{project_id}`**: Delete all chunks for a project

### Schemas

- **`drive.py`**: Pydantic schemas for Drive sync operations
- **`rag.py`**: Pydantic schemas for RAG operations

## Ingestion Pipeline

The ingestion pipeline follows these steps:

1. **Fetch Files**: Get list of files from Google Drive
2. **Download & Extract**: Download files and extract text
3. **Chunking**: Split text into chunks of ~500 tokens
4. **Embedding**: Generate embeddings using OpenAI's text-embedding-3-small model
5. **Storage**: Store embeddings in PostgreSQL with pgvector

## Frontend Integration

The frontend toggle in RightSidebar has been enhanced to:

1. Show loading state during sync
2. Display last synced time and file count
3. Show error messages
4. Provide a manual sync button

## Usage

### Authenticating with Google

1. Click the "Connect" button in the RightSidebar
2. Complete the Google OAuth flow
3. Once authenticated, enable the Drive toggle to sync files

### Syncing Google Drive Files

When the Drive toggle is turned ON, GAIA automatically:

1. Fetches files from Google Drive
2. Processes supported files (PDF, DOCX, TXT)
3. Indexes them in the RAG system

You can also manually trigger a sync by clicking the "Sync Google Drive" button.

### Using Drive Context in Chat

When Drive is enabled, GAIA automatically includes relevant information from your Drive files when answering questions. The system:

1. Embeds your query
2. Searches for semantically similar chunks in your indexed files
3. Includes relevant context in the prompt to the LLM
4. Generates responses that reference your Drive files

## Example API Calls

### Sync Drive Files

```python
import requests

response = requests.get(
    "http://localhost:8000/api/drive/sync",
    params={
        "project_id": "your-project-id",
        "user_id": "your-user-id"
    }
)

print(response.json())
```

### Query RAG System

```python
import requests

response = requests.post(
    "http://localhost:8000/api/rag/query",
    json={
        "project_id": "your-project-id",
        "query": "What was the Q2 sales forecast?",
        "top_k": 5
    }
)

results = response.json()["results"]
for result in results:
    print(f"Score: {result['similarity']}")
    print(f"Content: {result['text_chunk']}")
    print(f"Source: {result['metadata']['name']}")
    print("---")
```

## Implementation Details

### GoogleDriveLoader

The GoogleDriveLoader is inspired by LangChain's document loaders and provides methods for:

- Listing files in Google Drive
- Loading and processing documents
- Chunking text into manageable pieces

### RagService

The RagService handles:

- Embedding generation using OpenAI
- Vector storage in PostgreSQL with pgvector
- Similarity search for query results

## Error Handling

All endpoints include robust error handling:

- Clear, descriptive error messages
- Consistent JSON response format
- Logging for debugging

## Future Enhancements

- Support for more file types (spreadsheets, presentations)
- Improved chunking strategies
- Integration with other Google Workspace services (Gmail, Calendar)
- Microsoft 365 integration 