#!/usr/bin/env python3
"""
Production Fix Validation Script for GAIA

This script validates all the critical fixes:
1. OpenAI SDK usage with OpenAI() client
2. Vector type casting in SQL queries
3. Combined source query logic (source_filter=None)
4. Chat endpoint single query strategy
5. Metadata field consistency
"""

import asyncio
import logging
import os
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_openai_client_fix():
    """Test that OpenAI client is properly initialized"""
    logger.info("Testing OpenAI client initialization...")
    
    try:
        from app.utils.db_utils import get_embedding
        
        # Test embedding generation
        test_text = "This is a test for OpenAI client initialization."
        embedding = await get_embedding(test_text)
        
        if len(embedding) == 1536:  # text-embedding-3-small dimension
            logger.info("✅ OpenAI client initialization successful")
            return True
        else:
            logger.error(f"❌ Unexpected embedding dimension: {len(embedding)}")
            return False
            
    except Exception as e:
        logger.error(f"❌ OpenAI client test failed: {str(e)}")
        return False

async def test_vector_casting_fix():
    """Test that vector casting is working in SQL queries"""
    logger.info("Testing vector type casting in SQL queries...")
    
    try:
        from app.utils.db_utils import query_embeddings, store_embeddings, init_db
        
        # Initialize database
        await init_db()
        
        # Test project
        project_id = "test-vector-casting"
        
        # Create test data
        test_chunks = [{
            "text_chunk": "Vector casting test document with embedding similarity search.",
            "metadata": {
                "file_id": "test-vector",
                "file_name": "vector_test.pdf",
                "source": "upload",
                "mime_type": "application/pdf"
            }
        }]
        
        # Store test data
        stored_count = await store_embeddings(project_id, "anonymous", test_chunks)
        logger.info(f"Stored {stored_count} test chunks")
        
        # Test query with vector casting
        results = await query_embeddings(
            project_id=project_id,
            query="vector embedding similarity",
            source_filter='upload',
            similarity_threshold=0.1,
            limit=5
        )
        
        if len(results) > 0:
            logger.info(f"✅ Vector casting successful - found {len(results)} results")
            logger.info(f"  Similarity score: {results[0]['similarity']:.3f}")
            
            # Cleanup
            from app.utils.db_utils import get_db_connection
            conn = await get_db_connection()
            await conn.execute("DELETE FROM embeddings WHERE project_id = $1", project_id)
            await conn.close()
            
            return True
        else:
            logger.error("❌ Vector casting failed - no results returned")
            return False
            
    except Exception as e:
        logger.error(f"❌ Vector casting test failed: {str(e)}")
        return False

async def test_combined_source_query():
    """Test the critical combined source query logic"""
    logger.info("Testing combined source query (source_filter=None)...")
    
    try:
        from app.utils.db_utils import query_embeddings, store_embeddings, init_db
        
        # Initialize database
        await init_db()
        
        # Test project
        project_id = "test-combined-query"
        user_id = "test-user-123"
        
        # Create test data for uploaded files
        upload_chunks = [{
            "text_chunk": "This is an uploaded document about artificial intelligence and machine learning.",
            "metadata": {
                "file_id": "upload-test",
                "file_name": "AI_Guide.pdf",
                "source": "upload",
                "mime_type": "application/pdf"
            }
        }]
        
        # Create test data for Google Drive files
        drive_chunks = [{
            "text_chunk": "This is a Google Drive document about artificial intelligence applications.",
            "metadata": {
                "file_id": "drive-test",
                "name": "AI_Applications.docx",
                "source": "google_drive",
                "mime_type": "application/vnd.google-apps.document"
            }
        }]
        
        # Store test data
        upload_count = await store_embeddings(project_id, "anonymous", upload_chunks)
        drive_count = await store_embeddings(project_id, user_id, drive_chunks)
        
        logger.info(f"Stored {upload_count} upload chunks and {drive_count} drive chunks")
        
        # Test 1: Upload only
        upload_results = await query_embeddings(
            project_id=project_id,
            query="artificial intelligence",
            source_filter='upload',
            similarity_threshold=0.1,
            limit=5
        )
        
        # Test 2: Drive only
        drive_results = await query_embeddings(
            project_id=project_id,
            query="artificial intelligence",
            user_id=user_id,
            source_filter='google_drive',
            similarity_threshold=0.1,
            limit=5
        )
        
        # Test 3: CRITICAL - Combined query
        combined_results = await query_embeddings(
            project_id=project_id,
            query="artificial intelligence",
            user_id=user_id,
            source_filter=None,  # This should return BOTH sources
            similarity_threshold=0.1,
            limit=10
        )
        
        # Validate results
        upload_in_combined = sum(1 for r in combined_results if r['metadata']['source'] == 'upload')
        drive_in_combined = sum(1 for r in combined_results if r['metadata']['source'] == 'google_drive')
        
        logger.info(f"Upload-only query: {len(upload_results)} results")
        logger.info(f"Drive-only query: {len(drive_results)} results")
        logger.info(f"Combined query: {len(combined_results)} results ({upload_in_combined} upload, {drive_in_combined} drive)")
        
        # Cleanup
        from app.utils.db_utils import get_db_connection
        conn = await get_db_connection()
        await conn.execute("DELETE FROM embeddings WHERE project_id = $1", project_id)
        await conn.close()
        
        # Validation
        if len(upload_results) > 0 and len(drive_results) > 0 and len(combined_results) > 0:
            if upload_in_combined > 0 and drive_in_combined > 0:
                logger.info("✅ Combined source query successful - returned both upload and drive files")
                return True
            else:
                logger.error("❌ Combined query failed - missing one or both source types")
                return False
        else:
            logger.error("❌ Individual queries failed - cannot test combined query")
            return False
            
    except Exception as e:
        logger.error(f"❌ Combined source query test failed: {str(e)}")
        return False

async def test_metadata_consistency():
    """Test metadata field consistency"""
    logger.info("Testing metadata field consistency...")
    
    try:
        from app.utils.db_utils import query_embeddings, store_embeddings, init_db
        
        # Initialize database
        await init_db()
        
        # Test project
        project_id = "test-metadata"
        
        # Test data with different metadata formats
        test_chunks = [
            {
                "text_chunk": "Upload file with file_name field.",
                "metadata": {
                    "file_id": "upload-1",
                    "file_name": "upload_document.pdf",  # Upload format
                    "source": "upload"
                }
            },
            {
                "text_chunk": "Drive file with name field.",
                "metadata": {
                    "file_id": "drive-1", 
                    "name": "drive_document.docx",  # Drive format
                    "source": "google_drive"
                }
            }
        ]
        
        # Store test data
        stored_count = await store_embeddings(project_id, "test-user", test_chunks)
        
        # Query and check file name extraction
        results = await query_embeddings(
            project_id=project_id,
            query="document",
            user_id="test-user",
            source_filter=None,
            similarity_threshold=0.1,
            limit=10
        )
        
        # Validate file name extraction
        file_names = [r['file_name'] for r in results]
        expected_names = ["upload_document.pdf", "drive_document.docx"]
        
        if all(name in file_names for name in expected_names):
            logger.info("✅ Metadata consistency successful - both file_name and name fields handled")
            
            # Cleanup
            from app.utils.db_utils import get_db_connection
            conn = await get_db_connection()
            await conn.execute("DELETE FROM embeddings WHERE project_id = $1", project_id)
            await conn.close()
            
            return True
        else:
            logger.error(f"❌ Metadata consistency failed - expected {expected_names}, got {file_names}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Metadata consistency test failed: {str(e)}")
        return False

async def run_all_production_tests():
    """Run all production fix validation tests"""
    logger.info("🚀 Starting Production Fix Validation Tests")
    logger.info("=" * 60)
    
    tests = [
        ("OpenAI Client Fix", test_openai_client_fix),
        ("Vector Type Casting", test_vector_casting_fix),
        ("Combined Source Query (CRITICAL)", test_combined_source_query),
        ("Metadata Field Consistency", test_metadata_consistency),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running: {test_name}")
        logger.info("-" * 40)
        
        try:
            success = await test_func()
            results[test_name] = success
        except Exception as e:
            logger.error(f"❌ {test_name} failed with exception: {str(e)}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 Production Fix Validation Summary")
    logger.info("=" * 60)
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
    
    logger.info(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All production fixes validated successfully!")
        logger.info("\nGAIA is ready for production with:")
        logger.info("  ✅ Proper OpenAI client initialization")
        logger.info("  ✅ Vector type casting in SQL queries")
        logger.info("  ✅ Combined source query functionality")
        logger.info("  ✅ Consistent metadata handling")
    else:
        logger.warning(f"⚠️ {total - passed} tests failed. Please review the issues above.")
    
    return passed == total

def main():
    """Main test function"""
    try:
        success = asyncio.run(run_all_production_tests())
        exit_code = 0 if success else 1
    except KeyboardInterrupt:
        logger.info("\n⏹️ Tests interrupted by user")
        exit_code = 1
    except Exception as e:
        logger.error(f"💥 Test runner failed: {str(e)}")
        exit_code = 1
    
    exit(exit_code)

if __name__ == "__main__":
    main()
