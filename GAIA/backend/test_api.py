import requests
import json

# Base URL for the API
BASE_URL = "http://localhost:8000/api"

def test_drive_status():
    """Test the Drive status endpoint"""
    print("Testing Drive status endpoint...")
    response = requests.get(f"{BASE_URL}/drive/status/test-project-id")
    print(f"Status code: {response.status_code}")
    print(json.dumps(response.json(), indent=2))
    print()

def test_rag_status():
    """Test the RAG status endpoint"""
    print("Testing RAG status endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/rag/status/test-project-id")
        print(f"Status code: {response.status_code}")
        print(json.dumps(response.json(), indent=2))
    except Exception as e:
        print(f"Error: {str(e)}")
    print()

if __name__ == "__main__":
    print("Testing new API endpoints...")
    test_drive_status()
    test_rag_status()
    print("Done!") 