# 🔧 GAIA Backend Troubleshooting Guide

## 🚨 Current Issues from Logs

Based on the server logs, here are the issues and their solutions:

---

## ❌ Issue 1: User Credentials Not Found

**Error:**
```
WARNING:app.routes.auth:User credentials not found for user_id: e0919685-c57b-4b80-8569-5d3ff0c87fe1
INFO:     127.0.0.1:50109 - "GET /api/auth/google/credentials/e0919685-c57b-4b80-8569-5d3ff0c87fe1 HTTP/1.1" 404 Not Found
```

**Root Cause:**
The frontend is trying to access credentials for a user ID that doesn't exist in the backend's memory storage.

**Solutions:**

### 🔧 **Immediate Fix - Check Available Users**
```bash
# Call the debug endpoint to see what users are stored
curl http://localhost:8000/api/auth/debug/users
```

### 🔧 **Complete OAuth Flow**
1. **Start OAuth Flow:**
   ```bash
   # Navigate to this URL in browser
   http://localhost:8000/api/auth/google/login
   ```

2. **Complete Google Authorization:**
   - <PERSON><PERSON><PERSON> will redirect to Google OAuth consent screen
   - Grant permissions for Drive access
   - You'll be redirected back with a new user_id

3. **Use the New User ID:**
   - The redirect URL will contain the new user_id
   - Update your frontend to use this user_id

### 🔧 **Alternative - Mock Credentials for Testing**
Add this temporary endpoint to `auth.py` for testing:

```python
@router.post("/debug/mock-user")
async def create_mock_user():
    """Create a mock user for testing"""
    mock_user_id = "test-user-123"
    user_credentials[mock_user_id] = {
        "access_token": "mock_access_token",
        "refresh_token": "mock_refresh_token",
        "token_type": "Bearer",
        "scopes": ["https://www.googleapis.com/auth/drive.readonly"]
    }
    return {"user_id": mock_user_id, "message": "Mock user created"}
```

---

## ❌ Issue 2: Repeated Drive Status Calls

**Error:**
```
INFO:     127.0.0.1:50189 - "GET /api/drive/status/default HTTP/1.1" 200 OK
INFO:     127.0.0.1:50189 - "GET /api/drive/status/default HTTP/1.1" 200 OK
```

**Root Cause:**
Frontend is making repeated calls to `/api/drive/status/default` which should be limited.

**Solutions:**

### 🔧 **Frontend Fix - Add Debouncing**
Update the frontend to debounce status calls:

```typescript
// Add debouncing to prevent repeated calls
const debouncedStatusCheck = useCallback(
  debounce(async () => {
    try {
      const response = await fetch(`/api/drive/status/${projectId}`);
      const data = await response.json();
      setDriveStatus(data);
    } catch (error) {
      console.error('Drive status check failed:', error);
    }
  }, 1000), // 1 second debounce
  [projectId]
);
```

### 🔧 **Backend Fix - Add Rate Limiting**
Add simple rate limiting to the status endpoint:

```python
from time import time

# Add to drive_sync.py
last_status_check = {}

@router.get("/status/{project_id}")
async def get_drive_sync_status(project_id: str = Path(...)):
    # Simple rate limiting - max 1 call per 5 seconds per project
    now = time()
    if project_id in last_status_check:
        if now - last_status_check[project_id] < 5:
            # Return cached response or 429 Too Many Requests
            pass
    
    last_status_check[project_id] = now
    # ... rest of the function
```

---

## ✅ Verification Steps

### 1. **Check Backend Health**
```bash
curl http://localhost:8000/api/auth/debug/users
```
Expected: `{"total_users": 0, "user_ids": [], "oauth_states": 0}`

### 2. **Test Drive Status Endpoint**
```bash
curl http://localhost:8000/api/drive/status/default
```
Expected: Project stats or error message

### 3. **Complete OAuth Flow**
```bash
# Open in browser
http://localhost:8000/api/auth/google/login?project_id=default
```
Expected: Redirect to Google OAuth, then back with user_id

### 4. **Verify Credentials After OAuth**
```bash
# Use the user_id from step 3
curl http://localhost:8000/api/auth/google/credentials/{USER_ID}
```
Expected: `{"user_id": "...", "has_credentials": true, ...}`

---

## 🔧 Quick Fixes for Development

### **Option 1: Reset and Start Fresh**
1. Restart the backend server (Ctrl+C, then restart)
2. Clear browser localStorage/sessionStorage
3. Complete OAuth flow from scratch

### **Option 2: Mock User for Testing**
Add this to your auth.py temporarily:

```python
@router.get("/debug/create-test-user")
async def create_test_user():
    test_user_id = "e0919685-c57b-4b80-8569-5d3ff0c87fe1"  # Use the ID from logs
    user_credentials[test_user_id] = {
        "access_token": "test_token",
        "refresh_token": "test_refresh",
        "token_type": "Bearer",
        "scopes": ["https://www.googleapis.com/auth/drive.readonly"]
    }
    return {"message": f"Test user {test_user_id} created"}
```

Then call:
```bash
curl http://localhost:8000/api/auth/debug/create-test-user
```

### **Option 3: Update Frontend User ID**
If you have a valid user_id from a previous OAuth flow, update the frontend to use it.

---

## 🚀 Production Recommendations

### **1. Persistent Storage**
Replace in-memory storage with database:
```python
# Instead of: user_credentials = {}
# Use: Database table for user credentials
```

### **2. Token Refresh**
Implement automatic token refresh:
```python
async def refresh_token_if_needed(user_id: str):
    credentials = user_credentials[user_id]
    if is_token_expired(credentials):
        new_credentials = await refresh_google_token(credentials)
        user_credentials[user_id] = new_credentials
```

### **3. Rate Limiting**
Add proper rate limiting middleware:
```python
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)
```

### **4. Caching**
Add Redis caching for status endpoints:
```python
import redis
cache = redis.Redis(host='localhost', port=6379, db=0)

@router.get("/status/{project_id}")
@limiter.limit("10/minute")
async def get_drive_sync_status(request: Request, project_id: str):
    # Check cache first
    cached = cache.get(f"drive_status:{project_id}")
    if cached:
        return json.loads(cached)
    
    # ... compute status ...
    
    # Cache for 30 seconds
    cache.setex(f"drive_status:{project_id}", 30, json.dumps(response))
    return response
```

---

## 📞 Next Steps

1. **Immediate**: Use the debug endpoint to check current state
2. **Short-term**: Complete OAuth flow or create test user
3. **Medium-term**: Add rate limiting and caching
4. **Long-term**: Implement persistent storage and token refresh

The backend is running correctly - these are configuration and flow issues that can be resolved with the above steps.
