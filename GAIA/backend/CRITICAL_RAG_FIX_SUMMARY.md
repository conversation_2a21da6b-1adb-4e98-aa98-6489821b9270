# 🔧 CRITICAL RAG FIX - Combined Query Implementation

## 🎯 The Critical Issue That Was Fixed

The original implementation had a **fundamental flaw**: it could only query one source at a time (`'upload'` OR `'google_drive'`), but GAIA's requirement is to **retrieve context from BOTH uploaded and Drive files together** when Drive is enabled.

### ❌ Previous Broken Behavior:
```python
# This only worked for ONE source at a time
if use_drive_context:
    upload_results = await query_embeddings(..., source_filter='upload')
    drive_results = await query_embeddings(..., source_filter='google_drive')
    # Two separate queries, not optimal for ranking
```

### ✅ New Fixed Behavior:
```python
# This gets BOTH sources in one optimized query
if use_drive_context and user_id:
    all_results = await query_embeddings(..., source_filter=None)  # BOTH sources
else:
    all_results = await query_embeddings(..., source_filter='upload')  # Upload only
```

---

## 🛠️ Technical Implementation Details

### 1. Enhanced `query_embeddings()` Function (`db_utils.py`)

**Key Addition - Combined Source Query:**
```python
elif source_filter is None:
    # Handle both Google Drive and uploaded files
    drive_cond = f"(metadata->>'source' = 'google_drive'"
    if user_id:
        param_count += 1
        drive_cond += f" AND user_id = ${param_count}"
        where_params.append(user_id)
    drive_cond += ")"
    
    param_count += 1
    upload_cond = f"(metadata->>'source' = ${param_count})"
    where_params.append('upload')
    
    where_conditions.append(f"({drive_cond} OR {upload_cond})")
```

**Generated SQL Example:**
```sql
SELECT id, project_id, user_id, text_chunk, metadata,
       1 - (embedding <=> $1::vector) AS similarity
FROM embeddings
WHERE project_id = $2 
  AND 1 - (embedding <=> $1::vector) >= $3
  AND ((metadata->>'source' = 'google_drive' AND user_id = $4) 
       OR (metadata->>'source' = $5))
ORDER BY similarity DESC
LIMIT 10
```

### 2. Performance Optimizations Added

**Vector Type Casting:**
```python
# OLD: 1 - (embedding <=> $1)
# NEW: 1 - (embedding <=> $1::vector)
```
This ensures PostgreSQL correctly interprets the embedding as a vector type.

**Optimized LIMIT Handling:**
```python
# OLD: LIMIT ${param_count}  (dynamic parameter)
# NEW: LIMIT {limit}         (direct substitution)
```
Safer and more efficient since limit is not user input.

### 3. Streamlined Chat Logic (`chat.py`)

**Single Query Strategy:**
```python
if use_drive_context and user_id:
    # Get BOTH sources in one optimized query
    all_results = await query_embeddings(
        project_id=project_id,
        query=chat_message,
        user_id=user_id,
        source_filter=None,  # CRITICAL: This enables combined search
        similarity_threshold=0.3,
        limit=10  # Higher limit since we're getting both sources
    )
else:
    # Upload files only
    all_results = await query_embeddings(
        project_id=project_id,
        query=chat_message,
        source_filter='upload',
        similarity_threshold=0.3,
        limit=5
    )
```

---

## 🎯 Why This Fix Is Critical

### 1. **Proper Similarity Ranking**
- **Before**: Upload and Drive results ranked separately, then combined
- **After**: All results ranked together by similarity score across both sources

### 2. **Optimal Performance**
- **Before**: Two separate database queries
- **After**: Single optimized query with OR condition

### 3. **Correct Context Mixing**
- **Before**: Could miss relevant Drive content if upload results filled the limit
- **After**: Best results from both sources based on actual similarity

### 4. **User Privacy Maintained**
- Drive files still filtered by `user_id`
- Upload files remain accessible to all users in the project
- Source attribution preserved in responses

---

## 🧪 Testing the Fix

**Critical Test Case:**
```python
# This should return BOTH uploaded and Drive files
results = await query_embeddings(
    project_id="test",
    query="report",
    user_id="user123",
    source_filter=None,  # COMBINED QUERY
    similarity_threshold=0.3,
    limit=10
)

# Verify we get both source types
upload_count = sum(1 for r in results if r['metadata']['source'] == 'upload')
drive_count = sum(1 for r in results if r['metadata']['source'] == 'google_drive')

assert upload_count > 0 and drive_count > 0, "Should get both source types"
```

---

## 📊 Expected Behavior Changes

### Chat Responses Now Include:

1. **Mixed Source Context:**
   ```
   I found this content in your documents:

   [Document: Sales_Report.pdf (Uploaded File)]
   Q3 sales increased by 15%...

   [Document: Meeting_Notes.docx (Google Drive)]
   Discussed new product features...

   Sources used for this response: Sales_Report.pdf (Uploaded), Meeting_Notes.docx (Google Drive)
   ```

2. **Optimal Relevance Ranking:**
   - Most relevant chunks appear first regardless of source
   - Better context quality for user queries

3. **Efficient Database Usage:**
   - Single query instead of multiple queries
   - Reduced database load and latency

---

## 🔒 Security & Privacy Maintained

✅ **User Isolation**: Google Drive files still filtered by `user_id`  
✅ **Source Separation**: Clear attribution in responses  
✅ **Toggle Respect**: Drive context only when explicitly enabled  
✅ **Authentication Check**: Drive search requires valid `user_id`  

---

## 🚀 Performance Impact

- **Database Queries**: Reduced from 2 to 1 when Drive is enabled
- **Query Complexity**: Optimized OR condition with proper indexing
- **Memory Usage**: Single result set instead of merging multiple sets
- **Response Time**: Faster due to single database round-trip

---

## ✅ Verification Checklist

- [x] `source_filter=None` returns both uploaded and Drive files
- [x] `source_filter='upload'` returns only uploaded files  
- [x] `source_filter='google_drive'` returns only Drive files for the user
- [x] Vector casting `::vector` added for PostgreSQL compatibility
- [x] LIMIT handling optimized for performance
- [x] Chat endpoint uses combined query when Drive is enabled
- [x] Source attribution preserved in responses
- [x] User privacy maintained with proper filtering

The fix ensures GAIA now properly retrieves and combines context from both uploaded files and Google Drive documents in a single, optimized query while maintaining all security and privacy requirements.
