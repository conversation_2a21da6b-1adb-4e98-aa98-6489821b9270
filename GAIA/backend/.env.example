# GAIA Enhanced RAG System Configuration
# Copy this file to .env and fill in your actual values

# =============================================================================
# API Keys
# =============================================================================

# OpenAI API Key for embeddings (text-embedding-3-small)
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key for Claude 4 Sonnet
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# =============================================================================
# Database Configuration
# =============================================================================

# PostgreSQL connection string with pgvector extension
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/gaia

# Alternative database configuration (if not using DATABASE_URL)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=gaia
DB_USER=postgres
DB_PASSWORD=postgres

# =============================================================================
# Server Configuration
# =============================================================================

# FastAPI server settings
HOST=0.0.0.0
PORT=8000
LOG_LEVEL=info

# CORS settings
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# =============================================================================
# Google OAuth Configuration
# =============================================================================

# Google OAuth 2.0 credentials for Drive integration
GOOGLE_CLIENT_ID=your_google_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=http://localhost:8000/api/auth/google/callback

# Google Drive API scopes (comma-separated)
GOOGLE_SCOPES=https://www.googleapis.com/auth/drive.readonly,https://www.googleapis.com/auth/userinfo.email

# =============================================================================
# Background Tasks & Redis Configuration
# =============================================================================

# Redis URL for Celery task queue
REDIS_URL=redis://localhost:6379/0

# Celery configuration
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# =============================================================================
# RAG System Configuration
# =============================================================================

# Embedding model configuration
EMBEDDING_MODEL=text-embedding-3-small
EMBEDDING_DIMENSIONS=1536

# Claude model configuration
CLAUDE_MODEL=claude-3-sonnet-20240229
CLAUDE_MAX_TOKENS=4000

# Chunking configuration
DEFAULT_CHUNK_SIZE=500
DEFAULT_CHUNK_OVERLAP=50
MAX_CHUNKS_PER_QUERY=20

# Vector search configuration
SIMILARITY_THRESHOLD=0.7
MAX_CONTEXT_TOKENS=100000

# =============================================================================
# Google Drive Monitoring
# =============================================================================

# Drive sync configuration
DRIVE_SYNC_INTERVAL_MINUTES=30
DRIVE_MAX_FILES_PER_SYNC=1000
DRIVE_BATCH_SIZE=5

# Supported file types (comma-separated MIME types)
SUPPORTED_MIME_TYPES=application/pdf,application/vnd.google-apps.document,application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/msword,text/plain,application/vnd.google-apps.spreadsheet,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

# =============================================================================
# Performance & Optimization
# =============================================================================

# Thread pool configuration
INGESTION_THREAD_POOL_SIZE=4
CHAT_THREAD_POOL_SIZE=2

# Database connection pool
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# Task timeouts (in seconds)
TASK_TIME_LIMIT=1800  # 30 minutes
TASK_SOFT_TIME_LIMIT=1500  # 25 minutes

# =============================================================================
# Monitoring & Logging
# =============================================================================

# Logging configuration
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_FILE=logs/gaia.log

# Performance monitoring
ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_LOG_SLOW_QUERIES=true
SLOW_QUERY_THRESHOLD_SECONDS=5.0

# =============================================================================
# Security Configuration
# =============================================================================

# JWT configuration (if implementing authentication)
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Rate limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_BURST=10

# =============================================================================
# Development & Testing
# =============================================================================

# Environment mode
ENVIRONMENT=development  # development, staging, production

# Debug mode
DEBUG=true

# Test database (for running tests)
TEST_DATABASE_URL=postgresql://postgres:postgres@localhost:5432/gaia_test

# Mock external services in development
MOCK_GOOGLE_DRIVE=false
MOCK_OPENAI_API=false
MOCK_ANTHROPIC_API=false

# =============================================================================
# File Storage Configuration
# =============================================================================

# Local file storage for uploads
UPLOAD_DIR=uploads
MAX_UPLOAD_SIZE_MB=50

# Temporary file storage
TEMP_DIR=temp

# File cleanup settings
CLEANUP_TEMP_FILES_HOURS=24
CLEANUP_OLD_UPLOADS_DAYS=30

# =============================================================================
# Advanced Features
# =============================================================================

# Enable experimental features
ENABLE_ADVANCED_CHUNKING=true
ENABLE_QUERY_ANALYSIS=true
ENABLE_CONTEXT_OPTIMIZATION=true

# Vector index optimization
VECTOR_INDEX_LISTS=100
ENABLE_AUTO_INDEX_OPTIMIZATION=true

# Cache configuration
ENABLE_QUERY_CACHE=true
QUERY_CACHE_TTL_SECONDS=300
EMBEDDING_CACHE_TTL_SECONDS=3600
