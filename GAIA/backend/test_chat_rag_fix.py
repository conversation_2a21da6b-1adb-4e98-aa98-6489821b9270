#!/usr/bin/env python3
"""
Test script to verify the chat RAG fix is working correctly.

This script tests:
1. Enhanced query_embeddings function with source filtering
2. Chat endpoint integration with document context
3. Proper handling of uploaded vs Google Drive files
"""

import asyncio
import logging
import os
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_query_embeddings_function():
    """Test the enhanced query_embeddings function"""
    logger.info("Testing enhanced query_embeddings function...")

    try:
        from app.utils.db_utils import query_embeddings, store_embeddings, init_db

        # Initialize database
        await init_db()

        # Test project
        project_id = "test-chat-rag"
        user_id = "test-user"

        # Create test data for uploaded files
        upload_chunks = [
            {
                "text_chunk": "This is a test document about artificial intelligence and machine learning.",
                "metadata": {
                    "file_id": "upload-1",
                    "file_name": "AI_Overview.pdf",
                    "source": "upload",
                    "mime_type": "application/pdf"
                }
            },
            {
                "text_chunk": "Climate change is affecting global weather patterns significantly.",
                "metadata": {
                    "file_id": "upload-2",
                    "file_name": "Climate_Report.docx",
                    "source": "upload",
                    "mime_type": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                }
            }
        ]

        # Create test data for Google Drive files
        drive_chunks = [
            {
                "text_chunk": "The quarterly sales report shows a 15% increase in revenue.",
                "metadata": {
                    "file_id": "drive-1",
                    "name": "Q3_Sales_Report.pdf",
                    "source": "google_drive",
                    "mime_type": "application/pdf"
                }
            },
            {
                "text_chunk": "Meeting notes from the product strategy session on new features.",
                "metadata": {
                    "file_id": "drive-2",
                    "name": "Product_Meeting_Notes.docx",
                    "source": "google_drive",
                    "mime_type": "application/vnd.google-apps.document"
                }
            }
        ]

        # Store test data
        upload_count = await store_embeddings(project_id, "anonymous", upload_chunks)
        drive_count = await store_embeddings(project_id, user_id, drive_chunks)

        logger.info(f"Stored {upload_count} upload chunks and {drive_count} drive chunks")

        # Test 1: Query uploaded files only
        upload_results = await query_embeddings(
            project_id=project_id,
            query="artificial intelligence",
            source_filter='upload',
            similarity_threshold=0.1,
            limit=5
        )

        logger.info(f"Upload-only query returned {len(upload_results)} results")
        for result in upload_results:
            logger.info(f"  - {result['file_name']} (similarity: {result['similarity']:.3f})")

        # Test 2: Query Google Drive files only
        drive_results = await query_embeddings(
            project_id=project_id,
            query="sales report",
            user_id=user_id,
            source_filter='google_drive',
            similarity_threshold=0.1,
            limit=5
        )

        logger.info(f"Drive-only query returned {len(drive_results)} results")
        for result in drive_results:
            logger.info(f"  - {result['file_name']} (similarity: {result['similarity']:.3f})")

        # Test 3: Query all sources (CRITICAL TEST - this is the main fix)
        all_results = await query_embeddings(
            project_id=project_id,
            query="report",
            user_id=user_id,
            source_filter=None,  # This should return BOTH uploaded and Drive files
            similarity_threshold=0.1,
            limit=10
        )

        logger.info(f"Combined query (source_filter=None) returned {len(all_results)} results")
        upload_count = 0
        drive_count = 0
        for result in all_results:
            source = result['metadata'].get('source', 'unknown')
            if source == 'upload':
                upload_count += 1
            elif source == 'google_drive':
                drive_count += 1
            logger.info(f"  - {result['file_name']} ({source}, similarity: {result['similarity']:.3f})")

        logger.info(f"Combined query breakdown: {upload_count} uploaded, {drive_count} Drive files")

        # Verify we got results from both sources
        if upload_count > 0 and drive_count > 0:
            logger.info("✅ Combined query successfully returned both uploaded and Drive files")
        else:
            logger.warning(f"⚠️ Combined query missing sources - upload: {upload_count}, drive: {drive_count}")

        # Cleanup test data
        from app.utils.db_utils import get_db_connection
        conn = await get_db_connection()
        await conn.execute("DELETE FROM embeddings WHERE project_id = $1", project_id)
        await conn.close()

        logger.info("✅ query_embeddings function test passed")
        return True

    except Exception as e:
        logger.error(f"❌ query_embeddings function test failed: {str(e)}")
        return False

async def test_chat_endpoint_integration():
    """Test the chat endpoint with document context"""
    logger.info("Testing chat endpoint integration...")

    try:
        import httpx

        # Test data
        test_project_id = "test-chat-integration"
        test_user_id = "test-user-123"

        # First, add some test documents
        from app.utils.db_utils import store_embeddings, init_db

        await init_db()

        # Add test uploaded file
        upload_chunks = [{
            "text_chunk": "Python is a versatile programming language used for web development, data science, and automation.",
            "metadata": {
                "file_id": "test-upload",
                "file_name": "Python_Guide.pdf",
                "source": "upload",
                "mime_type": "application/pdf"
            }
        }]

        # Add test Google Drive file
        drive_chunks = [{
            "text_chunk": "The company's remote work policy allows employees to work from home three days per week.",
            "metadata": {
                "file_id": "test-drive",
                "name": "Remote_Work_Policy.docx",
                "source": "google_drive",
                "mime_type": "application/vnd.google-apps.document"
            }
        }]

        await store_embeddings(test_project_id, "anonymous", upload_chunks)
        await store_embeddings(test_project_id, test_user_id, drive_chunks)

        # Test chat requests
        base_url = "http://localhost:8000"

        async with httpx.AsyncClient() as client:
            # Test 1: Query about uploaded file (should always work)
            response1 = await client.post(
                f"{base_url}/api/chat/{test_project_id}",
                json={
                    "message": "Tell me about Python programming",
                    "project_id": test_project_id,
                    "use_drive_context": False,
                    "user_id": test_user_id
                },
                timeout=30.0
            )

            if response1.status_code == 200:
                logger.info("✅ Chat request for uploaded file succeeded")
            else:
                logger.error(f"❌ Chat request failed: {response1.status_code}")
                return False

            # Test 2: Query about Google Drive file (with Drive enabled)
            response2 = await client.post(
                f"{base_url}/api/chat/{test_project_id}",
                json={
                    "message": "What is the remote work policy?",
                    "project_id": test_project_id,
                    "use_drive_context": True,
                    "user_id": test_user_id
                },
                timeout=30.0
            )

            if response2.status_code == 200:
                logger.info("✅ Chat request for Google Drive file succeeded")
            else:
                logger.error(f"❌ Chat request failed: {response2.status_code}")
                return False

        # Cleanup
        from app.utils.db_utils import get_db_connection
        conn = await get_db_connection()
        await conn.execute("DELETE FROM embeddings WHERE project_id = $1", test_project_id)
        await conn.close()

        logger.info("✅ Chat endpoint integration test passed")
        return True

    except Exception as e:
        logger.error(f"❌ Chat endpoint integration test failed: {str(e)}")
        return False

async def run_all_tests():
    """Run all tests"""
    logger.info("🚀 Starting Chat RAG Fix Tests")
    logger.info("=" * 50)

    tests = [
        ("Enhanced query_embeddings Function", test_query_embeddings_function),
        ("Chat Endpoint Integration", test_chat_endpoint_integration),
    ]

    results = {}

    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running: {test_name}")
        logger.info("-" * 30)

        try:
            success = await test_func()
            results[test_name] = success
        except Exception as e:
            logger.error(f"❌ {test_name} failed with exception: {str(e)}")
            results[test_name] = False

    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 Test Summary")
    logger.info("=" * 50)

    passed = sum(1 for success in results.values() if success)
    total = len(results)

    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} - {test_name}")

    logger.info(f"\nResults: {passed}/{total} tests passed")

    if passed == total:
        logger.info("🎉 All tests passed! Chat RAG fix is working correctly.")
    else:
        logger.warning(f"⚠️ {total - passed} tests failed. Please check the issues above.")

    return passed == total

def main():
    """Main test function"""
    try:
        success = asyncio.run(run_all_tests())
        exit_code = 0 if success else 1
    except KeyboardInterrupt:
        logger.info("\n⏹️ Tests interrupted by user")
        exit_code = 1
    except Exception as e:
        logger.error(f"💥 Test runner failed: {str(e)}")
        exit_code = 1

    exit(exit_code)

if __name__ == "__main__":
    main()
