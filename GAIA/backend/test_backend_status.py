#!/usr/bin/env python3
"""
Backend Status Test Script for GAIA

This script tests the current backend status and provides solutions for common issues.
"""

import asyncio
import aiohttp
import logging
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:8000"

async def test_backend_health():
    """Test basic backend health"""
    logger.info("🔍 Testing backend health...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Test basic health
            async with session.get(f"{BASE_URL}/") as response:
                if response.status == 404:
                    logger.info("✅ Backend is running (404 expected for root)")
                else:
                    logger.info(f"✅ Backend is running (status: {response.status})")
                return True
    except Exception as e:
        logger.error(f"❌ Backend health check failed: {str(e)}")
        return False

async def test_auth_debug():
    """Test auth debug endpoint"""
    logger.info("🔍 Testing auth debug endpoint...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/api/auth/debug/users") as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ Auth debug successful:")
                    logger.info(f"   Total users: {data['total_users']}")
                    logger.info(f"   User IDs: {data['user_ids']}")
                    logger.info(f"   OAuth states: {data['oauth_states']}")
                    return data
                else:
                    logger.error(f"❌ Auth debug failed: {response.status}")
                    return None
    except Exception as e:
        logger.error(f"❌ Auth debug test failed: {str(e)}")
        return None

async def test_drive_status():
    """Test drive status endpoint"""
    logger.info("🔍 Testing drive status endpoint...")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/api/drive/status/default") as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ Drive status successful:")
                    logger.info(f"   Project ID: {data.get('project_id', 'N/A')}")
                    logger.info(f"   Is indexed: {data.get('is_indexed', False)}")
                    logger.info(f"   Total chunks: {data.get('total_chunks', 0)}")
                    return data
                else:
                    logger.error(f"❌ Drive status failed: {response.status}")
                    text = await response.text()
                    logger.error(f"   Response: {text}")
                    return None
    except Exception as e:
        logger.error(f"❌ Drive status test failed: {str(e)}")
        return None

async def test_credentials_endpoint():
    """Test the problematic credentials endpoint"""
    logger.info("🔍 Testing credentials endpoint with problematic user ID...")
    
    problematic_user_id = "e0919685-c57b-4b80-8569-5d3ff0c87fe1"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/api/auth/google/credentials/{problematic_user_id}") as response:
                if response.status == 404:
                    logger.info(f"✅ Expected 404 for non-existent user: {problematic_user_id}")
                    return True
                elif response.status == 200:
                    data = await response.json()
                    logger.info(f"✅ Credentials found for user: {problematic_user_id}")
                    logger.info(f"   Has credentials: {data.get('has_credentials', False)}")
                    return True
                else:
                    logger.error(f"❌ Unexpected status: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"❌ Credentials test failed: {str(e)}")
        return False

async def create_test_user():
    """Create a test user for development"""
    logger.info("🔧 Creating test user...")
    
    test_user_id = "e0919685-c57b-4b80-8569-5d3ff0c87fe1"  # Use the problematic ID
    
    # This would require adding the endpoint to auth.py
    logger.info(f"💡 To create a test user, add this endpoint to auth.py:")
    logger.info(f"""
@router.post("/debug/create-test-user")
async def create_test_user():
    test_user_id = "{test_user_id}"
    user_credentials[test_user_id] = {{
        "access_token": "test_token",
        "refresh_token": "test_refresh", 
        "token_type": "Bearer",
        "scopes": ["https://www.googleapis.com/auth/drive.readonly"]
    }}
    return {{"message": f"Test user {{test_user_id}} created"}}
""")
    
    logger.info(f"Then call: curl -X POST {BASE_URL}/api/auth/debug/create-test-user")

async def test_oauth_flow():
    """Test OAuth flow initiation"""
    logger.info("🔍 Testing OAuth flow initiation...")
    
    try:
        async with aiohttp.ClientSession() as session:
            # Test OAuth login endpoint
            async with session.get(f"{BASE_URL}/api/auth/google/login?project_id=default", allow_redirects=False) as response:
                if response.status in [302, 307]:
                    location = response.headers.get('Location', '')
                    if 'accounts.google.com' in location:
                        logger.info("✅ OAuth flow initiation successful")
                        logger.info(f"   Redirect URL: {location[:100]}...")
                        return True
                    else:
                        logger.warning(f"⚠️ Unexpected redirect: {location}")
                        return False
                else:
                    logger.error(f"❌ OAuth flow failed: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"❌ OAuth flow test failed: {str(e)}")
        return False

async def run_all_tests():
    """Run all backend tests"""
    logger.info("🚀 Starting Backend Status Tests")
    logger.info("=" * 50)
    
    tests = [
        ("Backend Health", test_backend_health),
        ("Auth Debug Endpoint", test_auth_debug),
        ("Drive Status Endpoint", test_drive_status),
        ("Credentials Endpoint", test_credentials_endpoint),
        ("OAuth Flow Initiation", test_oauth_flow),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running: {test_name}")
        logger.info("-" * 30)
        
        try:
            result = await test_func()
            results[test_name] = result is not False
        except Exception as e:
            logger.error(f"❌ {test_name} failed with exception: {str(e)}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 Backend Status Summary")
    logger.info("=" * 50)
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
    
    logger.info(f"\nResults: {passed}/{total} tests passed")
    
    # Provide solutions
    logger.info("\n" + "🔧 SOLUTIONS" + "=" * 40)
    
    if not results.get("Auth Debug Endpoint", False):
        logger.info("❌ Auth debug endpoint failed - check if backend is running")
    
    if not results.get("Credentials Endpoint", False):
        logger.info("❌ Credentials issue detected")
        await create_test_user()
    
    if not results.get("OAuth Flow Initiation", False):
        logger.info("❌ OAuth flow issue - check Google OAuth configuration")
    
    logger.info("\n💡 Quick fixes:")
    logger.info("1. Check debug endpoint: curl http://localhost:8000/api/auth/debug/users")
    logger.info("2. Start OAuth flow: http://localhost:8000/api/auth/google/login")
    logger.info("3. Check drive status: curl http://localhost:8000/api/drive/status/default")
    
    return passed == total

def main():
    """Main test function"""
    try:
        success = asyncio.run(run_all_tests())
        if success:
            logger.info("\n🎉 All tests passed! Backend is healthy.")
        else:
            logger.warning("\n⚠️ Some tests failed. Check the solutions above.")
    except KeyboardInterrupt:
        logger.info("\n⏹️ Tests interrupted by user")
    except Exception as e:
        logger.error(f"💥 Test runner failed: {str(e)}")

if __name__ == "__main__":
    main()
