#!/usr/bin/env python
"""
Create all necessary directories for the GAIA backend
This script ensures that all required directories exist
"""

import os
import sys

def create_directory(path):
    """Create a directory if it doesn't exist"""
    if not os.path.exists(path):
        os.makedirs(path)
        print(f"Created directory: {path}")
    else:
        print(f"Directory already exists: {path}")

def create_init_file(path):
    """Create an __init__.py file if it doesn't exist"""
    init_file = os.path.join(path, "__init__.py")
    if not os.path.exists(init_file):
        with open(init_file, "w") as f:
            f.write("# Automatically generated __init__.py file\n")
        print(f"Created __init__.py file: {init_file}")
    else:
        print(f"__init__.py file already exists: {init_file}")

def main():
    """Create all necessary directories"""
    # Base directory is the directory containing this script
    base_dir = os.path.dirname(os.path.abspath(__file__))
    app_dir = os.path.join(base_dir, "app")
    
    # Create app directory if it doesn't exist
    create_directory(app_dir)
    create_init_file(app_dir)
    
    # Create subdirectories
    directories = [
        os.path.join(app_dir, "routes"),
        os.path.join(app_dir, "services"),
        os.path.join(app_dir, "schemas"),
        os.path.join(app_dir, "utils"),
    ]
    
    for directory in directories:
        create_directory(directory)
        create_init_file(directory)
    
    print("All directories created successfully!")
    return 0

if __name__ == "__main__":
    sys.exit(main()) 