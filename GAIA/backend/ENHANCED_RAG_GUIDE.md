# GAIA Enhanced RAG System - Complete Implementation Guide

This guide provides comprehensive documentation for the enhanced RAG system that integrates uploaded files and Google Drive documents with PostgreSQL + pgvector, featuring Claude 4 Sonnet integration and automated monitoring.

## 🚀 Overview

The enhanced GAIA RAG system provides:

- **Dual Data Sources**: Upload files directly + Google Drive integration
- **Advanced Vector Storage**: PostgreSQL with pgvector extension
- **Intelligent Embeddings**: OpenAI text-embedding-3-small
- **Smart Response Generation**: Claude 4 Sonnet integration
- **Automated Monitoring**: Background tasks for Drive sync
- **Incremental Updates**: Only process new/changed files
- **Advanced Chunking**: Semantic-aware text splitting
- **Performance Optimization**: Efficient indexing and caching

## 📋 Prerequisites

### Required Software
- **Python 3.10+**
- **PostgreSQL 14+** with pgvector extension
- **Redis 6+** for background tasks
- **Node.js 16+** (for frontend)

### Required API Keys
- **OpenAI API Key** (for embeddings)
- **Anthropic API Key** (for Claude 4 Sonnet)
- **Google OAuth Credentials** (for Drive integration)

## 🛠️ Installation & Setup

### 1. Clone and Setup Backend

```bash
cd gaia-assistant/backend

# Install Python dependencies
pip install -r requirements.txt

# Copy environment configuration
cp .env.example .env
# Edit .env with your actual API keys and configuration
```

### 2. Configure Environment Variables

Edit your `.env` file with the following required values:

```bash
# API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/gaia

# Redis
REDIS_URL=redis://localhost:6379/0

# Google OAuth (optional, for Drive integration)
GOOGLE_CLIENT_ID=your_google_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_google_client_secret
```

### 3. Setup PostgreSQL with pgvector

```bash
# Install pgvector extension
# On Ubuntu/Debian:
sudo apt install postgresql-14-pgvector

# On macOS with Homebrew:
brew install pgvector

# Create database and enable extension
psql -U postgres
CREATE DATABASE gaia;
\c gaia
CREATE EXTENSION vector;
\q
```

### 4. Setup Redis

```bash
# On Ubuntu/Debian:
sudo apt install redis-server
sudo systemctl start redis-server

# On macOS with Homebrew:
brew install redis
brew services start redis
```

### 5. Run Setup Script

```bash
python setup_enhanced_rag.py
```

This script will:
- Validate environment variables
- Check dependencies
- Test database connections
- Initialize database tables
- Create required directories
- Validate API keys

## 🚀 Running the System

### 1. Start the FastAPI Server

```bash
python run.py
```

The API will be available at `http://localhost:8000`
API documentation at `http://localhost:8000/docs`

### 2. Start Background Task Workers

```bash
# In separate terminals:

# Start Celery worker
celery -A app.services.background_tasks worker --loglevel=info

# Start Celery beat (scheduler)
celery -A app.services.background_tasks beat --loglevel=info
```

### 3. Start Frontend (if needed)

```bash
cd ../  # Back to project root
npm install
npm start
```

## 📚 API Endpoints

### Enhanced RAG Endpoints

#### 1. Enhanced Query with Claude Integration
```http
POST /api/enhanced-rag/query
Content-Type: application/json

{
  "project_id": "project-123",
  "user_id": "user-456",
  "query": "What are the key findings from the Q2 sales report?",
  "max_chunks": 20,
  "use_claude": true
}
```

**Response:**
```json
{
  "response": "Based on the Q2 sales report...",
  "sources": [
    {
      "file_name": "Q2_Sales_Report.pdf",
      "file_id": "file-789",
      "similarity": 0.89,
      "chunk_preview": "Q2 sales exceeded expectations...",
      "web_view_link": "https://drive.google.com/file/d/..."
    }
  ],
  "query_analysis": {
    "intent": "question",
    "entities": ["Q2", "sales", "report"],
    "complexity": "moderate"
  },
  "context_stats": {
    "chunks_used": 5,
    "total_chunks_retrieved": 12,
    "context_length": 2048
  },
  "processing_time": 3.2
}
```

#### 2. Google Drive Sync with Change Detection
```http
POST /api/enhanced-rag/drive/sync
Content-Type: application/json

{
  "project_id": "project-123",
  "user_id": "user-456",
  "force_reindex": false
}
```

**Response:**
```json
{
  "status": "success",
  "sync_result": {
    "processed_files": 15,
    "new_files": 3,
    "updated_files": 2,
    "total_chunks": 847,
    "errors": [],
    "sync_time": "2024-01-15T10:30:00Z"
  },
  "message": "Sync completed: 15 files processed, 847 chunks indexed"
}
```

#### 3. Drive Status and Analytics
```http
GET /api/enhanced-rag/drive/status/project-123?user_id=user-456
```

**Response:**
```json
{
  "project_id": "project-123",
  "indexed_files": 45,
  "total_chunks": 2341,
  "last_sync": "2024-01-15T10:30:00Z",
  "first_sync": "2024-01-10T14:20:00Z",
  "file_types": [
    {
      "mime_type": "application/pdf",
      "file_count": 20,
      "chunk_count": 1200
    },
    {
      "mime_type": "application/vnd.google-apps.document",
      "file_count": 15,
      "chunk_count": 800
    }
  ]
}
```

#### 4. Change Detection
```http
POST /api/enhanced-rag/drive/detect-changes?project_id=project-123&user_id=user-456
```

#### 5. RAG Analytics
```http
GET /api/enhanced-rag/analytics/project-123
```

### File Upload Endpoints

#### Upload and Process Files
```http
POST /api/upload/files
Content-Type: multipart/form-data

files: [file1.pdf, file2.docx]
project_id: project-123
user_id: user-456
```

### Traditional RAG Endpoints

#### Direct PostgreSQL RAG
```http
POST /api/rag/ingest
POST /api/rag/query
GET /api/rag/status/{project_id}
DELETE /api/rag/project/{project_id}
```

## 🔧 Configuration Options

### Chunking Configuration
```python
# In .env file
DEFAULT_CHUNK_SIZE=500
DEFAULT_CHUNK_OVERLAP=50
MAX_CHUNKS_PER_QUERY=20
```

### Performance Tuning
```python
# Thread pools
INGESTION_THREAD_POOL_SIZE=4
CHAT_THREAD_POOL_SIZE=2

# Database
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# Vector search
SIMILARITY_THRESHOLD=0.7
MAX_CONTEXT_TOKENS=100000
```

### Background Tasks
```python
# Sync frequency
DRIVE_SYNC_INTERVAL_MINUTES=30

# Batch processing
DRIVE_BATCH_SIZE=5
DRIVE_MAX_FILES_PER_SYNC=1000
```

## 🔍 Advanced Features

### 1. Intelligent Query Processing
The system analyzes queries to:
- Determine intent and complexity
- Extract key entities
- Generate alternative phrasings
- Optimize retrieval strategy

### 2. Advanced Text Chunking
- **Semantic Awareness**: Preserves sentence and paragraph boundaries
- **Structure Detection**: Recognizes headers, lists, and sections
- **Adaptive Sizing**: Adjusts chunk size based on document type
- **Context Preservation**: Maintains document structure information

### 3. Multi-Strategy Retrieval
- **Direct Embedding Search**: Standard semantic similarity
- **Alternative Phrasing**: Searches with query variations
- **Entity-Based Search**: Focuses on extracted entities
- **Hybrid Ranking**: Combines multiple relevance signals

### 4. Automated Monitoring
- **Change Detection**: Monitors Google Drive for file changes
- **Incremental Updates**: Only processes new/modified files
- **Scheduled Sync**: Automatic background synchronization
- **Error Recovery**: Robust error handling and retry logic

### 5. Performance Optimization
- **Vector Indexing**: IVFFlat indexes for fast similarity search
- **Connection Pooling**: Efficient database connection management
- **Caching**: Query and embedding result caching
- **Batch Processing**: Efficient bulk operations

## 🐛 Troubleshooting

### Common Issues

#### 1. pgvector Extension Not Found
```bash
# Install pgvector
git clone https://github.com/pgvector/pgvector.git
cd pgvector
make
sudo make install

# Restart PostgreSQL
sudo systemctl restart postgresql
```

#### 2. Redis Connection Failed
```bash
# Check Redis status
redis-cli ping

# Start Redis if not running
sudo systemctl start redis-server
```

#### 3. API Key Issues
- Verify API keys in `.env` file
- Check API key permissions and quotas
- Test keys with simple API calls

#### 4. Google Drive Authentication
- Verify OAuth credentials
- Check redirect URI configuration
- Ensure proper scopes are requested

### Performance Issues

#### 1. Slow Vector Search
```sql
-- Check index usage
EXPLAIN ANALYZE SELECT * FROM embeddings 
WHERE project_id = 'test' 
ORDER BY embedding <=> '[0,0,0,...]' 
LIMIT 5;

-- Rebuild index if needed
REINDEX INDEX embeddings_embedding_idx;
```

#### 2. High Memory Usage
- Reduce `DEFAULT_CHUNK_SIZE`
- Lower `MAX_CHUNKS_PER_QUERY`
- Adjust thread pool sizes

## 📊 Monitoring & Maintenance

### Health Checks
```bash
# API health
curl http://localhost:8000/health

# Database connection
curl http://localhost:8000/api/rag/status/test-project

# Background tasks
celery -A app.services.background_tasks inspect active
```

### Maintenance Tasks
- **Weekly**: Run vector index optimization
- **Monthly**: Clean up old embeddings
- **Quarterly**: Review and optimize chunk sizes

## 🔐 Security Considerations

1. **API Keys**: Store securely, rotate regularly
2. **Database**: Use connection encryption
3. **Google OAuth**: Validate redirect URIs
4. **Rate Limiting**: Implement request throttling
5. **Input Validation**: Sanitize all user inputs

## 📈 Scaling Considerations

1. **Database**: Consider read replicas for high query loads
2. **Redis**: Use Redis Cluster for high availability
3. **Background Tasks**: Scale Celery workers horizontally
4. **Vector Search**: Consider specialized vector databases for very large datasets

This enhanced RAG system provides a robust, scalable foundation for enterprise knowledge management with intelligent document processing and retrieval capabilities.
