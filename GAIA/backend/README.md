# GAIA - Backend with Google Drive Integration

This is the backend for GAIA (GPT AI Assistant) with Google Drive integration via a RAG (Retrieval-Augmented Generation) pipeline.

## Features

- OAuth 2.0 integration with Google Drive
- File metadata synchronization from Google Drive
- Retrieval-Augmented Generation (RAG) pipeline:
  - File ingestion (PDF, DOCX, TXT)
  - Text extraction and chunking
  - Embedding generation using OpenAI
  - Vector storage using Pinecone
  - Query endpoint for semantic search

## Setup

### Prerequisites

- Python 3.10+
- OpenAI API key
- Google Cloud project with Google Drive API enabled
- Pinecone account with API key

### Configuration

1. **Create a Google Cloud Project**:
   - Go to the [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project
   - Enable the Google Drive API
   - Create OAuth credentials:
     - Create an OAuth 2.0 Client ID
     - Set the redirect URI to `http://localhost:8000/api/auth/google/callback`
     - Note your Client ID and Client Secret

2. **Create a Pinecone Index**:
   - Sign up for a [Pinecone account](https://www.pinecone.io/)
   - Create a new index with the following settings:
     - Dimensions: 1536 (OpenAI text-embedding-3-small dimension)
     - Metric: cosine
     - Note your API key and environment

3. **Environment Variables**:
   - Copy the `.env` file template
   - Fill in your:
     - OpenAI API key
     - Google OAuth credentials
     - Pinecone API key and environment

### Installation

```bash
pip install -r requirements.txt
```

### Running the Server

```bash
python run.py
```

## API Endpoints

### Google OAuth

- `GET /api/auth/google/login` - Initiates Google OAuth flow
- `GET /api/auth/google/callback` - OAuth callback endpoint
- `GET /api/auth/google/credentials/{user_id}` - Get user credentials status

### Google Drive

- `GET /api/drive/files` - Get user's Google Drive files
- `GET /api/drive/files/{file_id}` - Get metadata for a specific file
- `PUT /api/drive/files/{file_id}/indexed` - Mark file as indexed

### RAG Pipeline

- `POST /api/rag/ingest` - Ingest files into RAG pipeline
- `POST /api/rag/query` - Query the RAG pipeline
- `DELETE /api/rag/project/{project_id}` - Delete RAG data for a project
- `GET /api/rag/status/{project_id}` - Get RAG status for a project

### Chat

- `POST /api/chat/{project_id}` - Send a message and get a streaming response with RAG augmentation

## Usage Flow

1. **User Authentication**:
   - Frontend redirects user to `/api/auth/google/login`
   - User authenticates with Google
   - Frontend receives user_id

2. **File Metadata Sync**:
   - Frontend calls `/api/drive/files` to get file list
   - Files are displayed in the UI for selection

3. **RAG Ingestion**:
   - Frontend calls `/api/rag/ingest` with selected file IDs
   - Backend processes files and stores embeddings

4. **Chat Integration**:
   - When the Drive toggle is enabled in the UI, chat endpoint uses RAG context
   - The assistant responds with knowledge from Drive files 