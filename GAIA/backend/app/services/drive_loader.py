from typing import List, Dict, Any, Optional, Set
from googleapiclient.http import MediaIoBaseDownload
import io
import logging

# Local imports
from ..utils.google_auth import get_drive_service
from ..utils.rag_utils import extract_text_from_file, chunk_text

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Supported MIME types for ingestion
SUPPORTED_MIME_TYPES = [
    "application/pdf",
    "application/vnd.google-apps.document",  # Google Docs
    "application/vnd.google-apps.spreadsheet",  # Google Sheets
    "application/vnd.google-apps.presentation",  # Google Slides
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",  # DOCX
    "application/msword",  # DOC
    "text/plain",  # TXT
    "text/csv",  # CSV
]

class GoogleDriveLoader:
    """
    LangChain-inspired Google Drive Loader for GAIA.
    This class handles fetching, downloading, and processing files from Google Drive.
    """

    def __init__(self, credentials_dict: Dict[str, Any]):
        """Initialize the loader with Google Drive credentials"""
        self.drive_service = get_drive_service(credentials_dict)

    async def list_files(self,
                    max_results: int = 100,
                    file_types: Optional[List[str]] = None) -> List[Dict[str, Any]]:
        """
        List files from Google Drive, filtering by supported types
        """
        try:
            # Prepare query parameters
            query_params = {
                "pageSize": max_results,
                "fields": "files(id, name, mimeType, modifiedTime, webViewLink, size)",
                "orderBy": "modifiedTime desc"
            }

            # Build query for file types
            query = "trashed=false"

            if file_types:
                mime_type_conditions = [f"mimeType='{mime}'" for mime in file_types]
                query += f" AND ({' OR '.join(mime_type_conditions)})"
            else:
                # Default to supported types
                mime_type_conditions = [f"mimeType='{mime}'" or f"mimeType contains '{mime}'"
                                        for mime in SUPPORTED_MIME_TYPES]
                query += f" AND ({' OR '.join(mime_type_conditions)})"

            query_params["q"] = query

            # Execute the files.list API call
            results = self.drive_service.files().list(**query_params).execute()

            # Extract and return file metadata
            files = results.get("files", [])

            return [
                {
                    "file_id": file["id"],
                    "name": file["name"],
                    "mime_type": file["mimeType"],
                    "modified_time": file["modifiedTime"],
                    "web_view_link": file.get("webViewLink", ""),
                    "size": file.get("size", "0"),
                }
                for file in files
            ]

        except Exception as e:
            logger.error(f"Error listing Drive files: {str(e)}")
            raise

    async def load_document(self, file_id: str, file_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Load a document from Google Drive by its ID
        Returns the document text and metadata
        """
        try:
            mime_type = file_metadata["mime_type"]
            file_name = file_metadata["name"]

            # Handle Google Workspace files (Docs, Sheets, Slides) using export API
            if mime_type.startswith("application/vnd.google-apps."):
                file_content = await self._export_google_workspace_file(file_id, mime_type, file_name)
            else:
                # Handle regular files using media download
                file_content = await self._download_regular_file(file_id, file_name)

            if not file_content:
                logger.warning(f"No content downloaded from file: {file_name}")
                return None

            # Extract text based on mime type
            text = self._extract_text_with_fallback(file_content, mime_type, file_name)

            if not text:
                logger.warning(f"No text extracted from file: {file_name}")
                return None

            # Return document with text and metadata
            return {
                "text": text,
                "metadata": {
                    "file_id": file_id,
                    "name": file_name,
                    "mime_type": mime_type,
                    "modified_time": file_metadata["modified_time"],
                    "web_view_link": file_metadata.get("web_view_link", ""),
                    "source": "google_drive"
                }
            }

        except Exception as e:
            logger.error(f"Error loading document {file_id} ({file_metadata.get('name', 'Unknown')}): {str(e)}")
            return None

    async def _export_google_workspace_file(self, file_id: str, mime_type: str, file_name: str) -> bytes:
        """Export Google Workspace files (Docs, Sheets, Slides) to text format"""
        try:
            # Determine export MIME type based on Google Workspace type
            if mime_type == "application/vnd.google-apps.document":
                export_mime_type = "text/plain"
            elif mime_type == "application/vnd.google-apps.spreadsheet":
                export_mime_type = "text/csv"  # CSV for better structure
            elif mime_type == "application/vnd.google-apps.presentation":
                export_mime_type = "text/plain"
            else:
                logger.warning(f"Unsupported Google Workspace type: {mime_type} for file {file_name}")
                return None

            # Use export API for Google Workspace files
            request = self.drive_service.files().export_media(
                fileId=file_id,
                mimeType=export_mime_type
            )

            file_content = io.BytesIO()
            downloader = MediaIoBaseDownload(file_content, request)

            done = False
            while not done:
                status, done = downloader.next_chunk()
                if status:
                    progress = int(status.progress() * 100)
                    logger.info(f"Exporting {file_name}: {progress}%")

            content = file_content.getvalue()
            logger.info(f"Successfully exported Google Workspace file: {file_name} ({len(content)} bytes)")
            return content

        except Exception as e:
            logger.error(f"Error exporting Google Workspace file {file_id} ({file_name}): {str(e)}")
            return None

    async def _download_regular_file(self, file_id: str, file_name: str) -> bytes:
        """Download regular files (PDF, DOCX, etc.) using media download"""
        try:
            request = self.drive_service.files().get_media(fileId=file_id)
            file_content = io.BytesIO()
            downloader = MediaIoBaseDownload(file_content, request)

            done = False
            while not done:
                status, done = downloader.next_chunk()
                if status:
                    progress = int(status.progress() * 100)
                    logger.info(f"Downloading {file_name}: {progress}%")

            content = file_content.getvalue()
            logger.info(f"Successfully downloaded file: {file_name} ({len(content)} bytes)")
            return content

        except Exception as e:
            logger.error(f"Error downloading file {file_id} ({file_name}): {str(e)}")
            return None

    def _extract_text_with_fallback(self, file_content: bytes, mime_type: str, file_name: str) -> str:
        """Extract text with UTF-8 fallback handling"""
        try:
            # For text files or exported content, handle encoding properly
            if mime_type in ["text/plain", "text/csv"] or mime_type.startswith("application/vnd.google-apps."):
                try:
                    # Try UTF-8 first
                    text = file_content.decode("utf-8")
                except UnicodeDecodeError:
                    logger.warning(f"UTF-8 decode failed for {file_name}, trying latin-1")
                    try:
                        # Fallback to latin-1
                        text = file_content.decode("latin-1")
                    except UnicodeDecodeError:
                        logger.warning(f"Latin-1 decode failed for {file_name}, using error replacement")
                        # Last resort: replace bad bytes
                        text = file_content.decode("utf-8", errors="replace")

                return text.strip()
            else:
                # For binary files (PDF, DOCX), use the existing extraction logic
                return extract_text_from_file(file_content, mime_type)

        except Exception as e:
            logger.error(f"Error extracting text from {file_name}: {str(e)}")
            return ""

    async def load_and_chunk_documents(self,
                                 file_ids: List[str],
                                 file_metadata_list: List[Dict[str, Any]],
                                 chunk_size: int = 500,
                                 project_id: str = None,
                                 user_id: str = None) -> List[Dict[str, Any]]:
        """
        Load multiple documents and chunk them with deduplication
        Returns a list of chunks with metadata
        """
        chunks = []
        processed_files = 0
        skipped_files = 0

        # Create file_id to metadata mapping for quick lookup
        metadata_map = {metadata["file_id"]: metadata for metadata in file_metadata_list}

        # Track processed files for deduplication within this sync run
        processed_file_names = set()
        processed_file_ids = set()

        # Check for existing files in database if project_id and user_id provided
        existing_file_ids = set()
        if project_id and user_id:
            existing_file_ids = await self._get_existing_file_ids(project_id, user_id)

        for file_id in file_ids:
            if file_id not in metadata_map:
                logger.warning(f"No metadata found for file {file_id}")
                continue

            metadata = metadata_map[file_id]
            file_name = metadata["name"]

            # Deduplication checks
            if file_id in processed_file_ids:
                logger.info(f"Skipping duplicate file_id in this sync: {file_name} ({file_id})")
                skipped_files += 1
                continue

            if file_name in processed_file_names:
                logger.info(f"Skipping duplicate file name in this sync: {file_name}")
                skipped_files += 1
                continue

            if file_id in existing_file_ids:
                logger.info(f"Skipping already indexed file: {file_name} ({file_id})")
                skipped_files += 1
                continue

            # Mark as processed
            processed_file_ids.add(file_id)
            processed_file_names.add(file_name)

            # Load document
            document = await self.load_document(file_id, metadata)

            if not document:
                continue

            # Chunk text
            text_chunks = chunk_text(document["text"], chunk_size)

            if not text_chunks:
                logger.warning(f"No chunks generated from file: {file_name}")
                continue

            # Add chunks with metadata
            for i, chunk_content in enumerate(text_chunks):
                chunks.append({
                    "text_chunk": chunk_content,
                    "metadata": {
                        **document["metadata"],
                        "chunk_index": i
                    }
                })

            processed_files += 1
            logger.info(f"✅ Processed file {file_name}: {len(text_chunks)} chunks, MIME: {metadata['mime_type']}")

        logger.info(f"📊 Sync Summary: {processed_files} files processed, {skipped_files} files skipped, {len(chunks)} chunks generated")
        return chunks

    async def _get_existing_file_ids(self, project_id: str, user_id: str) -> set:
        """Get file IDs that are already indexed in the database"""
        try:
            from ..utils.db_utils import get_db_connection

            conn = await get_db_connection()

            # Query for existing Google Drive files for this project and user
            rows = await conn.fetch("""
                SELECT DISTINCT metadata->>'file_id' as file_id
                FROM embeddings
                WHERE project_id = $1
                  AND user_id = $2
                  AND metadata->>'source' = 'google_drive'
                  AND metadata->>'file_id' IS NOT NULL
            """, project_id, user_id)

            await conn.close()

            existing_ids = {row['file_id'] for row in rows if row['file_id']}
            logger.info(f"Found {len(existing_ids)} existing Drive files in database for project {project_id}")
            return existing_ids

        except Exception as e:
            logger.error(f"Error checking existing file IDs: {str(e)}")
            return set()