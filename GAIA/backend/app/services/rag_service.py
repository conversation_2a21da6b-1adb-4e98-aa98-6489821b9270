import json
import logging
from typing import List, Dict, Any
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

# Local imports
from ..utils.db_utils import get_embedding, get_db_connection

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create separate thread pools for different types of operations
chat_executor = ThreadPoolExecutor(max_workers=5, thread_name_prefix="chat")
ingestion_executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="ingestion")

class RagService:
    """
    RAG (Retrieval Augmented Generation) Service
    Handles embedding generation and vector storage/retrieval in PostgreSQL
    """

    @staticmethod
    async def embed_chunks(chunks: List[Dict[str, Any]], is_chat_query: bool = False) -> List[Dict[str, Any]]:
        """
        Generate embeddings for text chunks using OpenAI API
        Returns chunks with added embeddings

        Parameters:
        - chunks: List of text chunks to embed
        - is_chat_query: If True, uses the chat thread pool for higher priority
        """
        embedded_chunks = []

        # Select appropriate executor based on operation type
        executor = chat_executor if is_chat_query else ingestion_executor

        for chunk in chunks:
            try:
                # Generate embedding for the chunk using the appropriate executor
                embedding = await get_embedding(chunk["text_chunk"], executor=executor)

                # Add embedding to chunk
                embedded_chunks.append({
                    **chunk,
                    "embedding": embedding
                })

            except Exception as e:
                logger.error(f"Error embedding chunk: {str(e)}")
                # Skip this chunk
                continue

        return embedded_chunks

    @staticmethod
    async def store_chunks(
        project_id: str,
        user_id: str,
        chunks: List[Dict[str, Any]],
        is_chat_query: bool = False
    ) -> int:
        """
        Store text chunks and their embeddings in PostgreSQL

        Parameters:
        - project_id: Project ID
        - user_id: User ID
        - chunks: List of text chunks with metadata
        - is_chat_query: If True, uses chat thread pool for higher priority processing
        """
        conn = await get_db_connection()
        stored_count = 0

        try:
            # Select appropriate executor based on operation type
            executor = chat_executor if is_chat_query else ingestion_executor

            # Process each chunk
            for chunk in chunks:
                try:
                    text_chunk = chunk["text_chunk"]
                    metadata = chunk["metadata"]
                    embedding = chunk.get("embedding")

                    # Get embedding if not already provided
                    if not embedding:
                        embedding = await get_embedding(text_chunk, executor=executor)

                    # Store in database with proper vector format
                    await conn.execute("""
                        INSERT INTO embeddings (project_id, user_id, text_chunk, embedding, metadata)
                        VALUES ($1, $2, $3, $4, $5)
                    """, project_id, user_id, text_chunk, embedding, json.dumps(metadata))

                    stored_count += 1
                except Exception as e:
                    logger.error(f"Error storing chunk: {str(e)}")
                    continue

            return stored_count
        except Exception as e:
            logger.error(f"Error storing embeddings: {str(e)}")
            raise
        finally:
            await conn.close()

    @staticmethod
    async def query_chunks(
        project_id: str,
        query: str,
        limit: int = 5,
        similarity_threshold: float = 0.7
    ) -> List[Dict[str, Any]]:
        """
        Query for semantically similar chunks in PostgreSQL
        Returns chunks sorted by similarity

        This always uses the chat thread pool for priority processing
        """
        conn = await get_db_connection()

        try:
            # Get embedding for query - always use chat thread pool
            query_embedding = await get_embedding(query, executor=chat_executor)

            # Query database using vector similarity search
            rows = await conn.fetch("""
                SELECT id, project_id, text_chunk, metadata,
                       1 - (embedding <=> $1) AS similarity
                FROM embeddings
                WHERE project_id = $2
                  AND 1 - (embedding <=> $1) > $4
                ORDER BY similarity DESC
                LIMIT $3
            """, query_embedding, project_id, limit, similarity_threshold)

            # Format results
            results = []
            for row in rows:
                results.append({
                    "id": row["id"],
                    "project_id": row["project_id"],
                    "text_chunk": row["text_chunk"],
                    "metadata": json.loads(row["metadata"]),
                    "similarity": float(row["similarity"])
                })

            return results
        except Exception as e:
            logger.error(f"Error querying chunks: {str(e)}")
            raise
        finally:
            await conn.close()

    @staticmethod
    async def get_project_stats(project_id: str) -> Dict[str, Any]:
        """
        Get statistics about embeddings for a project
        """
        conn = await get_db_connection()

        try:
            # Get count of embeddings and unique files
            row = await conn.fetchrow("""
                SELECT
                    COUNT(*) as total_chunks,
                    COUNT(DISTINCT metadata->>'file_id') as unique_files,
                    MAX(created_at) as last_synced
                FROM embeddings
                WHERE project_id = $1
            """, project_id)

            return {
                "project_id": project_id,
                "total_chunks": row["total_chunks"],
                "unique_files": row["unique_files"],
                "is_indexed": row["total_chunks"] > 0,
                "last_synced": row["last_synced"].isoformat() if row["last_synced"] else None
            }
        except Exception as e:
            logger.error(f"Error getting project stats: {str(e)}")
            raise
        finally:
            await conn.close()

    @staticmethod
    async def delete_project_chunks(project_id: str) -> int:
        """
        Delete all chunks for a project
        """
        conn = await get_db_connection()

        try:
            result = await conn.execute("""
                DELETE FROM embeddings
                WHERE project_id = $1
            """, project_id)

            # Parse the DELETE count from result string like "DELETE 5"
            count = int(result.split()[1]) if result else 0
            return count
        except Exception as e:
            logger.error(f"Error deleting project chunks: {str(e)}")
            raise
        finally:
            await conn.close()