"""
Enhanced RAG Service for GAIA with Claude 4 Sonnet Integration

This service provides advanced RAG capabilities including:
- Intelligent query processing
- Context-aware retrieval
- Claude 4 Sonnet integration for response generation
- Advanced chunking strategies
"""

import os
import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
import json
from datetime import datetime
import tiktoken
from anthropic import Async<PERSON>nthropic

from ..utils.db_utils import get_db_connection, get_embedding
from ..services.rag_service import RagService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedRagService:
    """
    Enhanced RAG service with Claude 4 Sonnet integration and advanced features
    """
    
    def __init__(self):
        """Initialize the enhanced RAG service"""
        self.anthropic_client = AsyncAnthropic(
            api_key=os.getenv("ANTHROPIC_API_KEY")
        )
        self.base_rag_service = RagService()
        self.encoding = tiktoken.get_encoding("cl100k_base")  # GPT-4 encoding
        
        # Configuration
        self.max_context_tokens = 100000  # Claude 4 Sonnet context limit
        self.max_retrieval_chunks = 20
        self.similarity_threshold = 0.7
        self.chunk_overlap_ratio = 0.1
    
    async def intelligent_query_processing(self, query: str) -> Dict[str, Any]:
        """
        Process query to extract intent, entities, and generate search variations
        """
        try:
            # Use Claude to analyze the query
            analysis_prompt = f"""
            Analyze this user query for a RAG system and provide structured information:
            
            Query: "{query}"
            
            Please provide:
            1. Query intent (question, search, comparison, analysis, etc.)
            2. Key entities and concepts
            3. 2-3 alternative phrasings for better retrieval
            4. Suggested search terms
            5. Query complexity (simple, moderate, complex)
            
            Respond in JSON format:
            {{
                "intent": "string",
                "entities": ["entity1", "entity2"],
                "alternative_phrasings": ["phrase1", "phrase2"],
                "search_terms": ["term1", "term2"],
                "complexity": "simple|moderate|complex",
                "requires_context": true/false
            }}
            """
            
            response = await self.anthropic_client.messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=500,
                messages=[{"role": "user", "content": analysis_prompt}]
            )
            
            # Parse the JSON response
            analysis = json.loads(response.content[0].text)
            return analysis
            
        except Exception as e:
            logger.warning(f"Query analysis failed, using fallback: {str(e)}")
            # Fallback to simple analysis
            return {
                "intent": "question",
                "entities": [],
                "alternative_phrasings": [query],
                "search_terms": query.split(),
                "complexity": "moderate",
                "requires_context": True
            }
    
    async def advanced_retrieval(self, 
                               project_id: str, 
                               query: str, 
                               query_analysis: Dict[str, Any],
                               max_chunks: int = None) -> List[Dict[str, Any]]:
        """
        Perform advanced retrieval with multiple strategies
        """
        max_chunks = max_chunks or self.max_retrieval_chunks
        all_results = []
        
        try:
            # Strategy 1: Direct query embedding search
            direct_results = await self.base_rag_service.query_similar_chunks(
                project_id=project_id,
                query=query,
                limit=max_chunks // 2,
                similarity_threshold=self.similarity_threshold
            )
            all_results.extend(direct_results)
            
            # Strategy 2: Search with alternative phrasings
            for alt_phrase in query_analysis.get("alternative_phrasings", []):
                if alt_phrase != query:
                    alt_results = await self.base_rag_service.query_similar_chunks(
                        project_id=project_id,
                        query=alt_phrase,
                        limit=max_chunks // 4,
                        similarity_threshold=self.similarity_threshold
                    )
                    all_results.extend(alt_results)
            
            # Strategy 3: Entity-based search
            entities = query_analysis.get("entities", [])
            if entities:
                entity_query = " ".join(entities)
                entity_results = await self.base_rag_service.query_similar_chunks(
                    project_id=project_id,
                    query=entity_query,
                    limit=max_chunks // 4,
                    similarity_threshold=self.similarity_threshold * 0.8  # Lower threshold for entities
                )
                all_results.extend(entity_results)
            
            # Remove duplicates and sort by similarity
            seen_ids = set()
            unique_results = []
            for result in all_results:
                if result['id'] not in seen_ids:
                    seen_ids.add(result['id'])
                    unique_results.append(result)
            
            # Sort by similarity score
            unique_results.sort(key=lambda x: x['similarity'], reverse=True)
            
            return unique_results[:max_chunks]
            
        except Exception as e:
            logger.error(f"Error in advanced retrieval: {str(e)}")
            # Fallback to basic retrieval
            return await self.base_rag_service.query_similar_chunks(
                project_id=project_id,
                query=query,
                limit=max_chunks,
                similarity_threshold=self.similarity_threshold
            )
    
    def optimize_context_for_claude(self, 
                                  retrieved_chunks: List[Dict[str, Any]], 
                                  query: str) -> Tuple[str, List[Dict[str, Any]]]:
        """
        Optimize retrieved context for Claude's token limits and capabilities
        """
        # Calculate token counts
        query_tokens = len(self.encoding.encode(query))
        available_tokens = self.max_context_tokens - query_tokens - 1000  # Reserve for response
        
        # Sort chunks by relevance and build context
        context_parts = []
        used_tokens = 0
        used_chunks = []
        
        for chunk in retrieved_chunks:
            chunk_text = chunk['text_chunk']
            chunk_tokens = len(self.encoding.encode(chunk_text))
            
            if used_tokens + chunk_tokens <= available_tokens:
                # Add source information
                metadata = chunk['metadata']
                source_info = f"[Source: {metadata.get('name', 'Unknown')}]"
                
                context_part = f"{source_info}\n{chunk_text}\n"
                context_parts.append(context_part)
                used_chunks.append(chunk)
                used_tokens += len(self.encoding.encode(context_part))
            else:
                break
        
        context = "\n---\n".join(context_parts)
        
        logger.info(f"Optimized context: {len(used_chunks)} chunks, ~{used_tokens} tokens")
        
        return context, used_chunks
    
    async def generate_response_with_claude(self, 
                                          query: str, 
                                          context: str, 
                                          query_analysis: Dict[str, Any],
                                          used_chunks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Generate response using Claude 4 Sonnet with retrieved context
        """
        try:
            # Build the prompt based on query complexity
            complexity = query_analysis.get("complexity", "moderate")
            intent = query_analysis.get("intent", "question")
            
            if complexity == "simple":
                system_prompt = """You are a helpful AI assistant with access to relevant documents. 
                Provide clear, concise answers based on the provided context. 
                Always cite your sources when referencing specific information."""
            elif complexity == "complex":
                system_prompt = """You are an expert AI assistant capable of complex analysis and reasoning. 
                Use the provided context to give comprehensive, well-structured answers. 
                Break down complex topics, provide multiple perspectives when relevant, 
                and always cite your sources with specific document references."""
            else:
                system_prompt = """You are a knowledgeable AI assistant. 
                Use the provided context to answer questions accurately and helpfully. 
                Provide detailed explanations when needed and cite your sources."""
            
            # Construct the user prompt
            user_prompt = f"""
            Context from relevant documents:
            {context}
            
            User Question: {query}
            
            Please provide a comprehensive answer based on the context above. 
            If the context doesn't contain enough information to fully answer the question, 
            please say so and provide what information is available.
            
            When referencing information, please cite the specific source document.
            """
            
            # Generate response with Claude
            response = await self.anthropic_client.messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=4000,
                system=system_prompt,
                messages=[{"role": "user", "content": user_prompt}]
            )
            
            response_text = response.content[0].text
            
            # Prepare source information
            sources = []
            for chunk in used_chunks:
                metadata = chunk['metadata']
                source = {
                    "file_name": metadata.get('name', 'Unknown'),
                    "file_id": metadata.get('file_id', ''),
                    "similarity": chunk['similarity'],
                    "chunk_preview": chunk['text_chunk'][:200] + "..." if len(chunk['text_chunk']) > 200 else chunk['text_chunk']
                }
                if metadata.get('web_view_link'):
                    source["web_view_link"] = metadata['web_view_link']
                sources.append(source)
            
            return {
                "response": response_text,
                "sources": sources,
                "query_analysis": query_analysis,
                "context_stats": {
                    "chunks_used": len(used_chunks),
                    "total_chunks_retrieved": len(used_chunks),
                    "context_length": len(context)
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating Claude response: {str(e)}")
            # Fallback response
            return {
                "response": f"I apologize, but I encountered an error while processing your question: {str(e)}. Please try again.",
                "sources": [],
                "query_analysis": query_analysis,
                "context_stats": {
                    "chunks_used": 0,
                    "total_chunks_retrieved": 0,
                    "context_length": 0
                },
                "error": str(e)
            }
    
    async def enhanced_rag_query(self, 
                               project_id: str, 
                               query: str, 
                               max_chunks: int = None) -> Dict[str, Any]:
        """
        Perform complete enhanced RAG query with Claude response generation
        """
        start_time = datetime.now()
        
        try:
            # Step 1: Analyze the query
            logger.info(f"Analyzing query: {query}")
            query_analysis = await self.intelligent_query_processing(query)
            
            # Step 2: Perform advanced retrieval
            logger.info("Performing advanced retrieval")
            retrieved_chunks = await self.advanced_retrieval(
                project_id=project_id,
                query=query,
                query_analysis=query_analysis,
                max_chunks=max_chunks
            )
            
            if not retrieved_chunks:
                return {
                    "response": "I couldn't find any relevant information in your documents to answer this question.",
                    "sources": [],
                    "query_analysis": query_analysis,
                    "context_stats": {
                        "chunks_used": 0,
                        "total_chunks_retrieved": 0,
                        "context_length": 0
                    }
                }
            
            # Step 3: Optimize context for Claude
            logger.info("Optimizing context for Claude")
            context, used_chunks = self.optimize_context_for_claude(retrieved_chunks, query)
            
            # Step 4: Generate response with Claude
            logger.info("Generating response with Claude")
            result = await self.generate_response_with_claude(
                query=query,
                context=context,
                query_analysis=query_analysis,
                used_chunks=used_chunks
            )
            
            # Add timing information
            end_time = datetime.now()
            result["processing_time"] = (end_time - start_time).total_seconds()
            
            logger.info(f"Enhanced RAG query completed in {result['processing_time']:.2f} seconds")
            
            return result
            
        except Exception as e:
            logger.error(f"Error in enhanced RAG query: {str(e)}")
            return {
                "response": f"I encountered an error while processing your question: {str(e)}",
                "sources": [],
                "query_analysis": {"intent": "error", "complexity": "unknown"},
                "context_stats": {
                    "chunks_used": 0,
                    "total_chunks_retrieved": 0,
                    "context_length": 0
                },
                "error": str(e),
                "processing_time": 0
            }
