"""
Google Drive Monitoring Service for GAIA RAG System

This service provides automated monitoring and incremental updates for Google Drive files.
It tracks file changes, handles new uploads, and maintains sync state.
"""

import os
import asyncio
import logging
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timedelta
import json
from dataclasses import dataclass, asdict
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from ..utils.google_auth import get_drive_service
from ..utils.db_utils import get_db_connection
from .drive_loader import GoogleDriveLoader
from ..utils.db_utils import store_embeddings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class DriveFileState:
    """Represents the state of a Google Drive file for monitoring"""
    file_id: str
    name: str
    mime_type: str
    modified_time: str
    size: Optional[int]
    etag: str
    is_indexed: bool = False
    last_indexed: Optional[str] = None
    chunk_count: int = 0

@dataclass
class SyncResult:
    """Result of a sync operation"""
    processed_files: int
    new_files: int
    updated_files: int
    total_chunks: int
    errors: List[str]
    sync_time: str

class GoogleDriveMonitor:
    """
    Advanced Google Drive monitoring service with incremental updates
    """
    
    def __init__(self, credentials_dict: Dict[str, Any]):
        """Initialize the monitor with Google Drive credentials"""
        self.drive_service = get_drive_service(credentials_dict)
        self.drive_loader = GoogleDriveLoader(credentials_dict)
        self.supported_mime_types = [
            "application/pdf",
            "application/vnd.google-apps.document",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/msword",
            "text/plain",
            "application/vnd.google-apps.spreadsheet",  # Google Sheets
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",  # Excel
        ]
    
    async def get_file_state_from_db(self, project_id: str) -> Dict[str, DriveFileState]:
        """Get current file states from database"""
        conn = await get_db_connection()
        file_states = {}
        
        try:
            # Query for file states from metadata
            rows = await conn.fetch("""
                SELECT 
                    metadata->>'file_id' as file_id,
                    metadata->>'name' as name,
                    metadata->>'mime_type' as mime_type,
                    metadata->>'modified_time' as modified_time,
                    metadata->>'size' as size,
                    metadata->>'etag' as etag,
                    COUNT(*) as chunk_count,
                    MAX(created_at) as last_indexed
                FROM embeddings 
                WHERE project_id = $1 
                  AND metadata->>'source' = 'google_drive'
                GROUP BY 
                    metadata->>'file_id',
                    metadata->>'name',
                    metadata->>'mime_type',
                    metadata->>'modified_time',
                    metadata->>'size',
                    metadata->>'etag'
            """, project_id)
            
            for row in rows:
                file_id = row['file_id']
                if file_id:
                    file_states[file_id] = DriveFileState(
                        file_id=file_id,
                        name=row['name'] or '',
                        mime_type=row['mime_type'] or '',
                        modified_time=row['modified_time'] or '',
                        size=int(row['size']) if row['size'] else None,
                        etag=row['etag'] or '',
                        is_indexed=True,
                        last_indexed=row['last_indexed'].isoformat() if row['last_indexed'] else None,
                        chunk_count=row['chunk_count']
                    )
            
            return file_states
        except Exception as e:
            logger.error(f"Error getting file states from DB: {str(e)}")
            return {}
        finally:
            await conn.close()
    
    async def get_drive_files(self, max_results: int = 1000) -> List[Dict[str, Any]]:
        """Get files from Google Drive with change detection support"""
        try:
            # Build query for supported file types
            mime_conditions = [f"mimeType='{mime}'" for mime in self.supported_mime_types]
            query = f"trashed=false AND ({' OR '.join(mime_conditions)})"
            
            # Get files with etag for change detection
            results = self.drive_service.files().list(
                q=query,
                pageSize=max_results,
                fields="files(id, name, mimeType, modifiedTime, size, webViewLink, etag)",
                orderBy="modifiedTime desc"
            ).execute()
            
            return results.get('files', [])
        except HttpError as e:
            logger.error(f"Error fetching Drive files: {str(e)}")
            raise
    
    async def detect_changes(self, project_id: str) -> Dict[str, List[Dict[str, Any]]]:
        """Detect new and updated files in Google Drive"""
        # Get current state from database
        db_file_states = await self.get_file_state_from_db(project_id)
        
        # Get current files from Drive
        drive_files = await self.get_drive_files()
        
        new_files = []
        updated_files = []
        unchanged_files = []
        
        for drive_file in drive_files:
            file_id = drive_file['id']
            current_modified = drive_file['modifiedTime']
            current_etag = drive_file.get('etag', '')
            
            if file_id not in db_file_states:
                # New file
                new_files.append(drive_file)
            else:
                db_state = db_file_states[file_id]
                # Check if file has been modified
                if (current_modified != db_state.modified_time or 
                    current_etag != db_state.etag):
                    updated_files.append(drive_file)
                else:
                    unchanged_files.append(drive_file)
        
        return {
            'new_files': new_files,
            'updated_files': updated_files,
            'unchanged_files': unchanged_files
        }
    
    async def sync_files(self, project_id: str, user_id: str, 
                        force_reindex: bool = False) -> SyncResult:
        """
        Perform incremental sync of Google Drive files
        
        Args:
            project_id: Project identifier
            user_id: User identifier
            force_reindex: If True, reindex all files regardless of change status
        """
        sync_start = datetime.now()
        errors = []
        processed_files = 0
        total_chunks = 0
        
        try:
            if force_reindex:
                # Get all files for full reindex
                drive_files = await self.get_drive_files()
                new_files = drive_files
                updated_files = []
                
                # Clear existing embeddings for this project
                conn = await get_db_connection()
                try:
                    await conn.execute("""
                        DELETE FROM embeddings 
                        WHERE project_id = $1 AND metadata->>'source' = 'google_drive'
                    """, project_id)
                    logger.info(f"Cleared existing Drive embeddings for project {project_id}")
                finally:
                    await conn.close()
            else:
                # Detect changes
                changes = await self.detect_changes(project_id)
                new_files = changes['new_files']
                updated_files = changes['updated_files']
                
                # Remove old embeddings for updated files
                if updated_files:
                    conn = await get_db_connection()
                    try:
                        for file_info in updated_files:
                            await conn.execute("""
                                DELETE FROM embeddings 
                                WHERE project_id = $1 
                                  AND metadata->>'file_id' = $2
                            """, project_id, file_info['id'])
                    finally:
                        await conn.close()
            
            # Process new and updated files
            files_to_process = new_files + updated_files
            
            if not files_to_process:
                logger.info("No files to process - all files are up to date")
                return SyncResult(
                    processed_files=0,
                    new_files=0,
                    updated_files=0,
                    total_chunks=0,
                    errors=[],
                    sync_time=sync_start.isoformat()
                )
            
            # Process files in batches
            batch_size = 5
            for i in range(0, len(files_to_process), batch_size):
                batch = files_to_process[i:i + batch_size]
                
                # Prepare file metadata for batch processing
                file_metadata_list = []
                file_ids = []
                
                for file_info in batch:
                    file_ids.append(file_info['id'])
                    file_metadata_list.append({
                        'file_id': file_info['id'],
                        'name': file_info['name'],
                        'mime_type': file_info['mimeType'],
                        'modified_time': file_info['modifiedTime'],
                        'web_view_link': file_info.get('webViewLink', ''),
                        'size': file_info.get('size'),
                        'etag': file_info.get('etag', '')
                    })
                
                try:
                    # Load and chunk documents
                    chunks = await self.drive_loader.load_and_chunk_documents(
                        file_ids=file_ids,
                        file_metadata_list=file_metadata_list,
                        chunk_size=500
                    )
                    
                    if chunks:
                        # Enhance metadata with source information
                        for chunk in chunks:
                            chunk['metadata']['source'] = 'google_drive'
                            chunk['metadata']['indexed_at'] = sync_start.isoformat()
                        
                        # Store embeddings
                        stored_count = await store_embeddings(
                            project_id=project_id,
                            user_id=user_id,
                            chunks=chunks
                        )
                        
                        processed_files += len(batch)
                        total_chunks += stored_count
                        
                        logger.info(f"Processed batch: {len(batch)} files, {stored_count} chunks")
                    
                except Exception as e:
                    error_msg = f"Error processing batch: {str(e)}"
                    logger.error(error_msg)
                    errors.append(error_msg)
            
            return SyncResult(
                processed_files=processed_files,
                new_files=len(new_files),
                updated_files=len(updated_files),
                total_chunks=total_chunks,
                errors=errors,
                sync_time=sync_start.isoformat()
            )
            
        except Exception as e:
            error_msg = f"Error in sync operation: {str(e)}"
            logger.error(error_msg)
            errors.append(error_msg)
            
            return SyncResult(
                processed_files=processed_files,
                new_files=0,
                updated_files=0,
                total_chunks=total_chunks,
                errors=errors,
                sync_time=sync_start.isoformat()
            )
    
    async def get_sync_status(self, project_id: str) -> Dict[str, Any]:
        """Get detailed sync status for a project"""
        conn = await get_db_connection()
        
        try:
            # Get file statistics
            stats_row = await conn.fetchrow("""
                SELECT 
                    COUNT(DISTINCT metadata->>'file_id') as indexed_files,
                    COUNT(*) as total_chunks,
                    MAX(created_at) as last_sync,
                    MIN(created_at) as first_sync
                FROM embeddings 
                WHERE project_id = $1 AND metadata->>'source' = 'google_drive'
            """, project_id)
            
            # Get file breakdown by type
            type_breakdown = await conn.fetch("""
                SELECT 
                    metadata->>'mime_type' as mime_type,
                    COUNT(DISTINCT metadata->>'file_id') as file_count,
                    COUNT(*) as chunk_count
                FROM embeddings 
                WHERE project_id = $1 AND metadata->>'source' = 'google_drive'
                GROUP BY metadata->>'mime_type'
                ORDER BY file_count DESC
            """, project_id)
            
            return {
                'project_id': project_id,
                'indexed_files': stats_row['indexed_files'] if stats_row else 0,
                'total_chunks': stats_row['total_chunks'] if stats_row else 0,
                'last_sync': stats_row['last_sync'].isoformat() if stats_row and stats_row['last_sync'] else None,
                'first_sync': stats_row['first_sync'].isoformat() if stats_row and stats_row['first_sync'] else None,
                'file_types': [
                    {
                        'mime_type': row['mime_type'],
                        'file_count': row['file_count'],
                        'chunk_count': row['chunk_count']
                    }
                    for row in type_breakdown
                ]
            }
            
        except Exception as e:
            logger.error(f"Error getting sync status: {str(e)}")
            return {
                'project_id': project_id,
                'indexed_files': 0,
                'total_chunks': 0,
                'last_sync': None,
                'first_sync': None,
                'file_types': [],
                'error': str(e)
            }
        finally:
            await conn.close()
