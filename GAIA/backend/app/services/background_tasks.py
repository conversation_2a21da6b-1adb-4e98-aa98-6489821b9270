"""
Background Task System for GAIA RAG

This module provides background task processing for:
- Automated Google Drive monitoring
- Scheduled document reindexing
- Performance optimization tasks
- Cleanup operations
"""

import os
import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json
from celery import Celery
from celery.schedules import crontab
import redis

from ..services.drive_monitor import GoogleDriveMonitor
from ..services.enhanced_rag_service import EnhancedRagService
from ..utils.db_utils import get_db_connection

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Celery
celery_app = Celery(
    'gaia_tasks',
    broker=os.getenv('REDIS_URL', 'redis://localhost:6379/0'),
    backend=os.getenv('REDIS_URL', 'redis://localhost:6379/0')
)

# Configure Celery
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_time_limit=30 * 60,  # 30 minutes
    task_soft_time_limit=25 * 60,  # 25 minutes
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
)

# Schedule periodic tasks
celery_app.conf.beat_schedule = {
    'monitor-drive-changes': {
        'task': 'gaia_tasks.monitor_all_drive_changes',
        'schedule': crontab(minute='*/30'),  # Every 30 minutes
    },
    'cleanup-old-embeddings': {
        'task': 'gaia_tasks.cleanup_old_embeddings',
        'schedule': crontab(hour=2, minute=0),  # Daily at 2 AM
    },
    'optimize-vector-indexes': {
        'task': 'gaia_tasks.optimize_vector_indexes',
        'schedule': crontab(hour=3, minute=0, day_of_week=0),  # Weekly on Sunday at 3 AM
    },
}

# Redis client for task coordination
redis_client = redis.Redis.from_url(os.getenv('REDIS_URL', 'redis://localhost:6379/0'))

class TaskManager:
    """
    Manages background tasks and scheduling
    """
    
    @staticmethod
    def get_active_projects() -> List[Dict[str, Any]]:
        """Get list of active projects that need monitoring"""
        try:
            # This would typically come from a projects database
            # For now, we'll get projects that have embeddings
            import asyncio
            return asyncio.run(TaskManager._get_active_projects_async())
        except Exception as e:
            logger.error(f"Error getting active projects: {str(e)}")
            return []
    
    @staticmethod
    async def _get_active_projects_async() -> List[Dict[str, Any]]:
        """Async helper to get active projects"""
        conn = await get_db_connection()
        try:
            rows = await conn.fetch("""
                SELECT 
                    project_id,
                    user_id,
                    COUNT(*) as chunk_count,
                    MAX(created_at) as last_activity
                FROM embeddings 
                WHERE metadata->>'source' = 'google_drive'
                  AND created_at > NOW() - INTERVAL '30 days'
                GROUP BY project_id, user_id
                HAVING COUNT(*) > 0
                ORDER BY last_activity DESC
            """)
            
            return [
                {
                    "project_id": row["project_id"],
                    "user_id": row["user_id"],
                    "chunk_count": row["chunk_count"],
                    "last_activity": row["last_activity"].isoformat()
                }
                for row in rows
            ]
        finally:
            await conn.close()
    
    @staticmethod
    def schedule_drive_sync(project_id: str, user_id: str, delay_seconds: int = 0):
        """Schedule a Drive sync task"""
        sync_drive_files.apply_async(
            args=[project_id, user_id],
            countdown=delay_seconds
        )
        logger.info(f"Scheduled Drive sync for project {project_id} in {delay_seconds} seconds")
    
    @staticmethod
    def get_task_status(task_id: str) -> Dict[str, Any]:
        """Get status of a background task"""
        try:
            result = celery_app.AsyncResult(task_id)
            return {
                "task_id": task_id,
                "status": result.status,
                "result": result.result if result.ready() else None,
                "traceback": result.traceback if result.failed() else None
            }
        except Exception as e:
            return {
                "task_id": task_id,
                "status": "ERROR",
                "error": str(e)
            }

@celery_app.task(bind=True, name='gaia_tasks.sync_drive_files')
def sync_drive_files(self, project_id: str, user_id: str, force_reindex: bool = False):
    """
    Background task to sync Google Drive files
    """
    try:
        logger.info(f"Starting background Drive sync for project {project_id}")
        
        # Update task status
        self.update_state(
            state='PROGRESS',
            meta={'status': 'Initializing Drive sync', 'progress': 0}
        )
        
        # This would need to be adapted to work with the async Drive monitor
        # For now, we'll use a synchronous wrapper
        import asyncio
        
        async def run_sync():
            # Get user credentials from Redis or database
            # This is a simplified version - in production, you'd need proper credential management
            credentials_key = f"user_credentials:{user_id}"
            credentials_data = redis_client.get(credentials_key)
            
            if not credentials_data:
                raise Exception(f"No credentials found for user {user_id}")
            
            credentials = json.loads(credentials_data)
            
            # Initialize Drive monitor
            drive_monitor = GoogleDriveMonitor(credentials)
            
            # Update progress
            self.update_state(
                state='PROGRESS',
                meta={'status': 'Detecting changes', 'progress': 25}
            )
            
            # Perform sync
            sync_result = await drive_monitor.sync_files(
                project_id=project_id,
                user_id=user_id,
                force_reindex=force_reindex
            )
            
            return sync_result
        
        # Run the async sync
        sync_result = asyncio.run(run_sync())
        
        # Update final status
        result = {
            'status': 'completed',
            'processed_files': sync_result.processed_files,
            'new_files': sync_result.new_files,
            'updated_files': sync_result.updated_files,
            'total_chunks': sync_result.total_chunks,
            'errors': sync_result.errors,
            'sync_time': sync_result.sync_time
        }
        
        logger.info(f"Background Drive sync completed for project {project_id}")
        return result
        
    except Exception as e:
        logger.error(f"Error in background Drive sync: {str(e)}")
        self.update_state(
            state='FAILURE',
            meta={'error': str(e)}
        )
        raise

@celery_app.task(name='gaia_tasks.monitor_all_drive_changes')
def monitor_all_drive_changes():
    """
    Periodic task to monitor Drive changes for all active projects
    """
    try:
        logger.info("Starting periodic Drive monitoring for all projects")
        
        active_projects = TaskManager.get_active_projects()
        
        if not active_projects:
            logger.info("No active projects found for monitoring")
            return {"status": "no_projects", "monitored_projects": 0}
        
        monitored_count = 0
        errors = []
        
        for project in active_projects:
            try:
                project_id = project["project_id"]
                user_id = project["user_id"]
                
                # Check if sync is needed (only if last activity was more than 1 hour ago)
                last_activity = datetime.fromisoformat(project["last_activity"])
                if datetime.now() - last_activity > timedelta(hours=1):
                    # Schedule a sync with a random delay to avoid overwhelming the system
                    import random
                    delay = random.randint(0, 300)  # 0-5 minutes
                    
                    TaskManager.schedule_drive_sync(project_id, user_id, delay)
                    monitored_count += 1
                    
            except Exception as e:
                error_msg = f"Error monitoring project {project.get('project_id', 'unknown')}: {str(e)}"
                logger.error(error_msg)
                errors.append(error_msg)
        
        result = {
            "status": "completed",
            "monitored_projects": monitored_count,
            "total_projects": len(active_projects),
            "errors": errors
        }
        
        logger.info(f"Periodic monitoring completed: {monitored_count} projects scheduled for sync")
        return result
        
    except Exception as e:
        logger.error(f"Error in periodic Drive monitoring: {str(e)}")
        return {"status": "error", "error": str(e)}

@celery_app.task(name='gaia_tasks.cleanup_old_embeddings')
def cleanup_old_embeddings():
    """
    Cleanup old or orphaned embeddings
    """
    try:
        logger.info("Starting cleanup of old embeddings")
        
        import asyncio
        
        async def run_cleanup():
            conn = await get_db_connection()
            try:
                # Delete embeddings older than 90 days with no recent activity
                result = await conn.execute("""
                    DELETE FROM embeddings 
                    WHERE created_at < NOW() - INTERVAL '90 days'
                      AND project_id NOT IN (
                          SELECT DISTINCT project_id 
                          FROM embeddings 
                          WHERE created_at > NOW() - INTERVAL '30 days'
                      )
                """)
                
                # Parse deletion count
                deleted_count = int(result.split()[1]) if result else 0
                
                return {"deleted_embeddings": deleted_count}
                
            finally:
                await conn.close()
        
        result = asyncio.run(run_cleanup())
        
        logger.info(f"Cleanup completed: {result['deleted_embeddings']} old embeddings removed")
        return result
        
    except Exception as e:
        logger.error(f"Error in cleanup task: {str(e)}")
        return {"status": "error", "error": str(e)}

@celery_app.task(name='gaia_tasks.optimize_vector_indexes')
def optimize_vector_indexes():
    """
    Optimize vector indexes for better performance
    """
    try:
        logger.info("Starting vector index optimization")
        
        import asyncio
        
        async def run_optimization():
            conn = await get_db_connection()
            try:
                # Reindex vector indexes for better performance
                await conn.execute("REINDEX INDEX embeddings_embedding_idx")
                
                # Update table statistics
                await conn.execute("ANALYZE embeddings")
                
                # Get index statistics
                stats = await conn.fetchrow("""
                    SELECT 
                        schemaname,
                        tablename,
                        indexname,
                        idx_tup_read,
                        idx_tup_fetch
                    FROM pg_stat_user_indexes 
                    WHERE indexname = 'embeddings_embedding_idx'
                """)
                
                return {
                    "status": "completed",
                    "index_stats": dict(stats) if stats else None
                }
                
            finally:
                await conn.close()
        
        result = asyncio.run(run_optimization())
        
        logger.info("Vector index optimization completed")
        return result
        
    except Exception as e:
        logger.error(f"Error in index optimization: {str(e)}")
        return {"status": "error", "error": str(e)}

# Task monitoring utilities
def get_all_active_tasks() -> List[Dict[str, Any]]:
    """Get all currently active background tasks"""
    try:
        inspect = celery_app.control.inspect()
        active_tasks = inspect.active()
        
        if not active_tasks:
            return []
        
        all_tasks = []
        for worker, tasks in active_tasks.items():
            for task in tasks:
                all_tasks.append({
                    "worker": worker,
                    "task_id": task["id"],
                    "task_name": task["name"],
                    "args": task["args"],
                    "kwargs": task["kwargs"],
                    "time_start": task["time_start"]
                })
        
        return all_tasks
        
    except Exception as e:
        logger.error(f"Error getting active tasks: {str(e)}")
        return []

def cancel_task(task_id: str) -> bool:
    """Cancel a background task"""
    try:
        celery_app.control.revoke(task_id, terminate=True)
        logger.info(f"Cancelled task {task_id}")
        return True
    except Exception as e:
        logger.error(f"Error cancelling task {task_id}: {str(e)}")
        return False
