from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional, Dict
import logging
from ..utils.google_auth import get_drive_service
from ..routes.auth import user_credentials
from ..schemas import DriveFilesResponse, DriveFileMetadata

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/drive", tags=["drive"])

# In-memory storage for file metadata by project
# In production, this would be stored in a database
project_files = {}

@router.get("/files")
async def get_files(
    user_id: str,
    project_id: str,
    page_token: Optional[str] = None,
    mime_types: Optional[str] = None,
    max_results: int = 100
) -> DriveFilesResponse:
    """
    Fetch and store metadata from user's Google Drive
    """
    try:
        # Check if user has credentials
        if user_id not in user_credentials:
            raise HTTPException(status_code=401, detail="User not authenticated with Google Drive")
        
        # Get Google Drive service
        drive_service = get_drive_service(user_credentials[user_id])
        
        # Prepare query parameters
        query_params = {
            "pageSize": max_results,
            "fields": "nextPageToken, files(id, name, mimeType, modifiedTime, webViewLink, size)"
        }
        
        if page_token:
            query_params["pageToken"] = page_token
        
        # Filter by mime types if provided
        query = ""
        if mime_types:
            mime_type_list = mime_types.split(",")
            mime_type_conditions = [f"mimeType contains '{mime}'" for mime in mime_type_list]
            query = " or ".join(mime_type_conditions)
            query = f"({query}) and trashed=false"
        else:
            # Exclude trashed files
            query = "trashed=false"
        
        query_params["q"] = query
        
        # Execute the files.list API call
        results = drive_service.files().list(**query_params).execute()
        
        # Extract file metadata
        files = results.get("files", [])
        next_page_token = results.get("nextPageToken", None)
        
        # Convert to our schema
        file_metadata_list = []
        for file in files:
            file_metadata = DriveFileMetadata(
                file_id=file["id"],
                name=file["name"],
                mime_type=file["mimeType"],
                modified_time=file["modifiedTime"],
                web_view_link=file.get("webViewLink"),
                size=file.get("size"),
                is_indexed=False
            )
            file_metadata_list.append(file_metadata)
        
        # Store metadata in memory by project
        if project_id not in project_files:
            project_files[project_id] = {}
        
        # Update the project's file metadata
        for metadata in file_metadata_list:
            project_files[project_id][metadata.file_id] = metadata.dict()
        
        return DriveFilesResponse(files=file_metadata_list, next_page_token=next_page_token)
    
    except Exception as e:
        logger.error(f"Error fetching Drive files: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching Drive files: {str(e)}")

@router.get("/files/{file_id}")
async def get_file_metadata(
    user_id: str,
    project_id: str,
    file_id: str
) -> DriveFileMetadata:
    """
    Get metadata for a specific file
    """
    # Check if we have cached metadata
    if project_id in project_files and file_id in project_files[project_id]:
        return DriveFileMetadata(**project_files[project_id][file_id])
    
    # If not cached, fetch from Google Drive
    try:
        # Check if user has credentials
        if user_id not in user_credentials:
            raise HTTPException(status_code=401, detail="User not authenticated with Google Drive")
        
        # Get Google Drive service
        drive_service = get_drive_service(user_credentials[user_id])
        
        # Get file metadata
        file = drive_service.files().get(
            fileId=file_id,
            fields="id, name, mimeType, modifiedTime, webViewLink, size"
        ).execute()
        
        # Convert to our schema
        file_metadata = DriveFileMetadata(
            file_id=file["id"],
            name=file["name"],
            mime_type=file["mimeType"],
            modified_time=file["modifiedTime"],
            web_view_link=file.get("webViewLink"),
            size=file.get("size"),
            is_indexed=False
        )
        
        # Cache the metadata
        if project_id not in project_files:
            project_files[project_id] = {}
        
        project_files[project_id][file_id] = file_metadata.dict()
        
        return file_metadata
    
    except Exception as e:
        logger.error(f"Error fetching file metadata: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error fetching file metadata: {str(e)}")

@router.put("/files/{file_id}/indexed")
async def mark_file_as_indexed(
    user_id: str,
    project_id: str,
    file_id: str,
    is_indexed: bool = True
) -> DriveFileMetadata:
    """
    Mark a file as indexed or not indexed
    """
    # Check if we have the file metadata
    if project_id not in project_files or file_id not in project_files[project_id]:
        raise HTTPException(status_code=404, detail="File metadata not found")
    
    # Update the indexed status
    project_files[project_id][file_id]["is_indexed"] = is_indexed
    
    # Return the updated metadata
    return DriveFileMetadata(**project_files[project_id][file_id]) 