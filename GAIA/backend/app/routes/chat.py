from fastapi import APIRouter, HTTPException, Request, Query
from fastapi.responses import StreamingResponse
from typing import Dict, Any, Optional
from openai import OpenAI
import os
import json
import asyncio
from ..schemas import Message
import logging
from datetime import datetime
from uuid import uuid4
from ..utils.db_utils import query_embeddings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/chat", tags=["chat"])

# In-memory storage for projects and messages
# In a production app, this would be replaced with a database
projects_db: Dict[str, Dict[str, Any]] = {}

# Create OpenAI client
def get_openai_client():
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise HTTPException(status_code=500, detail="OpenAI API key not found")

    return OpenAI(api_key=api_key)


@router.post("/{project_id}")
@router.get("/{project_id}")
async def chat(
    project_id: str,
    request: Request,
    message: Optional[str] = Query(None),
    use_drive_context: bool = Query(True)
):
    """
    Chat endpoint that streams responses from GPT-4.1 nano.
    Can be called with either a query parameter or a JSON body.
    """
    try:
        # Check if message is in query parameter
        user_id = None
        if message:
            chat_message = message
            project_id_from_client = project_id
        else:
            # Try to get from request body
            body = await request.json()
            chat_message = body.get("message")
            project_id_from_client = body.get("project_id", project_id)
            use_drive_context = body.get("use_drive_context", True)
            user_id = body.get("user_id")

            if not chat_message:
                raise HTTPException(status_code=400, detail="Message is required")
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Invalid request: {str(e)}")

    # Check if project exists or create
    if project_id not in projects_db and project_id != project_id_from_client:
        raise HTTPException(status_code=404, detail=f"Project {project_id} not found")

    # Create project if it doesn't exist (just for demo)
    if project_id not in projects_db:
        projects_db[project_id] = {
            "id": project_id,
            "messages": []
        }

    # Create user message
    user_message = Message(
        id=str(uuid4()),
        sender="user",
        content=chat_message,
        timestamp=datetime.now().strftime("%H:%M %p")
    )

    # Add user message to project
    projects_db[project_id]["messages"].append(user_message.model_dump())

    # Get OpenAI client
    openai_client = get_openai_client()

    # Prepare messages for OpenAI API
    messages = [
        {"role": "system", "content": "You are GAIA, a helpful, GPT-powered AI assistant for enterprise knowledge workers. When referencing documents, always include the document name and be specific about what information came from which document."}
    ]

    # Perform semantic search for document context
    try:
        logger.info(f"Searching for context - use_drive_context: {use_drive_context}, user_id: {user_id}")

        # Determine search strategy based on Drive toggle
        if use_drive_context and user_id:
            # Search both uploaded files and Google Drive files together
            all_results = await query_embeddings(
                project_id=project_id,
                query=chat_message,
                user_id=user_id,
                source_filter=None,  # This will search both sources
                similarity_threshold=0.3,
                limit=10  # Increased limit since we're getting both sources
            )
        else:
            # Only search uploaded files when Drive is disabled or user not authenticated
            all_results = await query_embeddings(
                project_id=project_id,
                query=chat_message,
                source_filter='upload',
                similarity_threshold=0.3,
                limit=5
            )

        # Format context for GPT
        context_message = ""
        sources = []

        if all_results:
            context_message = "I found this content in your documents:\n\n"

            # Process all results and categorize by source
            for result in all_results:
                file_name = result['file_name']
                source_type = result['metadata'].get('source', 'unknown')

                if source_type == 'upload':
                    context_message += f"[Document: {file_name} (Uploaded File)]\n{result['text_chunk']}\n\n"
                    sources.append(f"{file_name} (Uploaded)")
                elif source_type == 'google_drive':
                    context_message += f"[Document: {file_name} (Google Drive)]\n{result['text_chunk']}\n\n"
                    sources.append(f"{file_name} (Google Drive)")
                else:
                    context_message += f"[Document: {file_name}]\n{result['text_chunk']}\n\n"
                    sources.append(file_name)

            # Add context to messages
            messages.append({
                "role": "system",
                "content": context_message
            })

            # List sources used
            if sources:
                messages.append({
                    "role": "system",
                    "content": f"Sources used for this response: {', '.join(sources)}"
                })

            logger.info(f"Added context from {len(all_results)} documents (Drive enabled: {use_drive_context})")
        else:
            messages.append({
                "role": "system",
                "content": "No relevant documents were found in the user's files for this query."
            })
            logger.info("No relevant documents found")

    except Exception as e:
        logger.error(f"Error getting document context: {str(e)}")
        # Continue without document context if there's an error
        messages.append({
            "role": "system",
            "content": f"Note: I encountered an issue retrieving document context: {str(e)}. I'll answer based on my general knowledge."
        })

    # Add conversation history (limited to last 10 messages for context window management)
    history = projects_db[project_id]["messages"][-10:]
    for msg in history:
        role = "user" if msg["sender"] == "user" else "assistant"
        messages.append({"role": role, "content": msg["content"]})

    try:
        # Create streaming response
        return StreamingResponse(
            generate_response(openai_client, messages, project_id),
            media_type="text/event-stream"
        )
    except Exception as e:
        logger.error(f"Error generating response: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


async def generate_response(openai_client, messages, project_id):
    """
    Generator function to stream OpenAI responses
    """
    response_id = str(uuid4())
    timestamp = datetime.now().strftime("%H:%M %p")

    try:
        # Full content to be stored in the database
        full_content = ""

        # Stream response from OpenAI
        stream = openai_client.chat.completions.create(
            model="gpt-4o-mini",  # Use current available model
            messages=messages,
            stream=True,
            max_tokens=1500,  # Increased from 1000 for more detailed responses
            temperature=0.7  # Added temperature for slightly more creative responses
        )

        for chunk in stream:
            if chunk.choices and chunk.choices[0].delta.content:
                content_chunk = chunk.choices[0].delta.content
                full_content += content_chunk

                # Send SSE format response
                chat_response = {
                    "id": response_id,
                    "sender": "ai",
                    "content": content_chunk,
                    "timestamp": timestamp,
                    "is_chunk": True
                }

                yield f"data: {json.dumps(chat_response)}\n\n"
                await asyncio.sleep(0.01)  # Small delay to prevent overloading

        # Save the full message to the project
        ai_message = Message(
            id=response_id,
            sender="ai",
            content=full_content,
            timestamp=timestamp
        )
        projects_db[project_id]["messages"].append(ai_message.model_dump())

        # Send a completion message
        yield f"data: {json.dumps({'is_complete': True})}\n\n"

    except Exception as e:
        logger.error(f"Error in stream: {str(e)}")
        error_response = {
            "id": response_id,
            "sender": "ai",
            "content": f"Sorry, an error occurred: {str(e)}",
            "timestamp": timestamp,
            "is_error": True
        }
        yield f"data: {json.dumps(error_response)}\n\n"