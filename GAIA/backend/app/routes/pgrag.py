from fastapi import APIRouter, HTTPException, Depends, Body
from typing import List, Dict, Any, Optional
import logging
from ..schemas import (
    PgRagIngestRequest,
    PgRagIngestResponse,
    PgRagQueryRequest,
    PgRagQueryResponse,
    PgRagQueryResult,
    PgRagStatsResponse
)
from ..utils.db_utils import (
    init_db,
    store_embeddings,
    query_embeddings,
    delete_project_embeddings,
    get_project_stats
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(tags=["pgrag"])


# Initialize database on startup
async def initialize_db():
    await init_db()


@router.post("/ingest")
async def ingest_chunks(request: PgRagIngestRequest) -> PgRagIngestResponse:
    """
    Ingest text chunks into the PostgreSQL vector database
    
    This endpoint:
    1. Takes a list of text chunks and their metadata
    2. Generates embeddings for each chunk
    3. Stores them in PostgreSQL with pgvector
    """
    try:
        # Format chunks for processing
        chunks = [
            {"text_chunk": chunk.text_chunk, "metadata": chunk.metadata}
            for chunk in request.chunks
        ]
        
        # Store embeddings
        stored_count = await store_embeddings(
            project_id=request.project_id,
            user_id=request.user_id,
            chunks=chunks
        )
        
        return PgRagIngestResponse(
            status="success",
            processed_chunks=stored_count,
            message=f"Successfully ingested {stored_count} chunks"
        )
    
    except Exception as e:
        logger.error(f"Error in PostgreSQL RAG ingest: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error in PostgreSQL RAG ingest: {str(e)}"
        )


@router.post("/query")
async def query_chunks(request: PgRagQueryRequest) -> PgRagQueryResponse:
    """
    Query the PostgreSQL vector database for semantically similar text chunks
    
    This endpoint:
    1. Takes a query string
    2. Generates an embedding for the query
    3. Searches for similar vectors in PostgreSQL
    4. Returns the most relevant chunks
    """
    try:
        # Query embeddings
        results = await query_embeddings(
            project_id=request.project_id,
            query=request.query,
            limit=request.top_k
        )
        
        # Convert to response format
        query_results = [
            PgRagQueryResult(
                id=result["id"],
                text_chunk=result["text_chunk"],
                metadata=result["metadata"],
                similarity=result["similarity"]
            )
            for result in results
        ]
        
        return PgRagQueryResponse(results=query_results)
    
    except Exception as e:
        logger.error(f"Error in PostgreSQL RAG query: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error in PostgreSQL RAG query: {str(e)}"
        )


@router.delete("/project/{project_id}")
async def delete_project(project_id: str):
    """
    Delete all embeddings for a specific project
    """
    try:
        deleted_count = await delete_project_embeddings(project_id)
        
        return {
            "status": "success",
            "deleted_chunks": deleted_count,
            "message": f"Successfully deleted {deleted_count} chunks for project {project_id}"
        }
    
    except Exception as e:
        logger.error(f"Error deleting project embeddings: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error deleting project embeddings: {str(e)}"
        )


@router.get("/status/{project_id}")
async def get_status(project_id: str) -> PgRagStatsResponse:
    """
    Get stats about a project's indexed content
    """
    try:
        stats = await get_project_stats(project_id)
        return PgRagStatsResponse(**stats)
    
    except Exception as e:
        logger.error(f"Error getting project stats: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting project stats: {str(e)}"
        ) 