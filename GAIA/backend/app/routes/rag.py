from fastapi import APIRouter, Depends, HTTPException, Body
from typing import List, Dict, Any
import logging
from ..utils.google_auth import get_drive_service
from ..routes.auth import user_credentials
from ..routes.drive import project_files, mark_file_as_indexed
from ..utils.rag_utils import (
    download_file_from_drive,
    extract_text_from_file,
    chunk_text,
    get_embeddings_for_text
)
from ..utils.db_utils import (
    store_embeddings,
    query_embeddings,
    delete_project_embeddings,
    get_project_stats
)
from ..schemas import RagIngestRequest, RagIngestResponse, RagQueryRequest, RagQueryResponse, RagQueryResult

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/rag", tags=["rag"])

# In-memory storage for project RAG settings
# In production, this would be stored in a database
project_rag_settings = {}

@router.post("/ingest")
async def ingest_files(request: RagIngestRequest):
    """
    Ingest files from Google Drive into RAG pipeline using PostgreSQL
    """
    user_id = request.user_id if hasattr(request, 'user_id') else None
    project_id = request.project_id
    file_ids = request.file_ids
    
    try:
        # Check if user has credentials
        if user_id not in user_credentials:
            raise HTTPException(status_code=401, detail="User not authenticated with Google Drive")
        
        # Get Google Drive service
        drive_service = get_drive_service(user_credentials[user_id])
        
        # Process each file
        processed_files = 0
        total_chunks = 0
        
        for file_id in file_ids:
            try:
                # Get file metadata
                if project_id not in project_files or file_id not in project_files[project_id]:
                    # If metadata is not cached, skip this file
                    logger.warning(f"Skipping file {file_id}, metadata not found")
                    continue
                
                file_metadata = project_files[project_id][file_id]
                mime_type = file_metadata["mime_type"]
                
                # Check if file type is supported
                if not (
                    "pdf" in mime_type 
                    or "document" in mime_type 
                    or "text/plain" in mime_type
                ):
                    logger.warning(f"Skipping unsupported file type: {mime_type}")
                    continue
                
                # Download file content from Google Drive
                file_content = download_file_from_drive(drive_service, file_id)
                
                # Extract text from file
                text = extract_text_from_file(file_content, mime_type)
                
                if not text:
                    logger.warning(f"No text extracted from file: {file_id}")
                    continue
                
                # Chunk text
                chunks = chunk_text(text)
                
                if not chunks:
                    logger.warning(f"No chunks generated from file: {file_id}")
                    continue
                
                # Prepare metadata and chunks for PostgreSQL storage
                db_chunks = []
                for i, chunk_text in enumerate(chunks):
                    metadata = {
                        "file_id": file_id,
                        "file_name": file_metadata["name"],
                        "mime_type": mime_type,
                        "chunk_index": i,
                        "modified_time": file_metadata["modified_time"],
                        "web_view_link": file_metadata.get("web_view_link", ""),
                    }
                    
                    db_chunks.append({
                        "text_chunk": chunk_text,
                        "metadata": metadata
                    })
                
                # Store in PostgreSQL
                stored_count = await store_embeddings(
                    project_id=project_id,
                    user_id=user_id or "anonymous",
                    chunks=db_chunks
                )
                
                # Mark file as indexed
                await mark_file_as_indexed(user_id, project_id, file_id, True)
                
                processed_files += 1
                total_chunks += stored_count
                
            except Exception as e:
                logger.error(f"Error processing file {file_id}: {str(e)}")
                continue
        
        # Return response
        return RagIngestResponse(
            processed_files=processed_files,
            total_chunks=total_chunks,
            message=f"Successfully ingested {processed_files} files with {total_chunks} chunks"
        )
        
    except Exception as e:
        logger.error(f"Error in RAG ingest: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error in RAG ingest: {str(e)}")

@router.post("/query")
async def query_rag(request: RagQueryRequest):
    """
    Query RAG pipeline using PostgreSQL
    """
    project_id = request.project_id
    query = request.query
    top_k = request.top_k
    include_content = request.include_content
    
    try:
        # Query PostgreSQL for similar vectors
        results = await query_embeddings(
            project_id=project_id,
            query=query,
            limit=top_k
        )
        
        # Format results
        query_results = []
        
        for result in results:
            metadata = result["metadata"]
            
            result_item = RagQueryResult(
                file_id=metadata["file_id"],
                file_name=metadata["file_name"],
                chunk_content=result["text_chunk"] if include_content else "",
                score=result["similarity"],
                modified_time=metadata["modified_time"],
                web_view_link=metadata.get("web_view_link", "")
            )
            
            query_results.append(result_item)
        
        return RagQueryResponse(results=query_results)
    
    except Exception as e:
        logger.error(f"Error in RAG query: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error in RAG query: {str(e)}")

@router.delete("/project/{project_id}")
async def delete_project_data(project_id: str):
    """
    Delete all RAG data for a project from PostgreSQL
    """
    try:
        # Delete all embeddings for this project
        deleted_count = await delete_project_embeddings(project_id)
        
        # If we have project files stored, reset their indexed status
        if project_id in project_files:
            for file_id in project_files[project_id]:
                project_files[project_id][file_id]["is_indexed"] = False
        
        return {
            "message": f"Successfully deleted RAG data for project {project_id}",
            "deleted_chunks": deleted_count
        }
    
    except Exception as e:
        logger.error(f"Error deleting project RAG data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting project RAG data: {str(e)}")

@router.get("/status/{project_id}")
async def get_rag_status(project_id: str):
    """
    Get RAG status for a project
    """
    try:
        # Get stats from PostgreSQL
        stats = await get_project_stats(project_id)
        
        return {
            "project_id": project_id,
            "indexed_files": stats["unique_files"],
            "total_chunks": stats["total_chunks"],
            "is_indexed": stats["is_indexed"],
            "completion_percentage": 100 if not project_files.get(project_id) else int(stats["unique_files"] / max(len(project_files.get(project_id, {})), 1) * 100)
        }
    
    except Exception as e:
        logger.error(f"Error getting RAG status: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting RAG status: {str(e)}") 