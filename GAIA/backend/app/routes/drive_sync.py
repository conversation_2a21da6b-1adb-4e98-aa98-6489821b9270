from fastapi import APIRouter, HTTPException, Query, Path, BackgroundTasks
from typing import Optional, List, Dict, Any
import logging
from datetime import datetime
import asyncio
from concurrent.futures import ThreadPoolExecutor

from ..schemas.drive import DriveSyncRequest, DriveSyncResponse, DriveFileMetadata
from ..services.drive_loader import GoogleDriveLoader
from ..services.rag_service import RagService
from ..routes.auth import user_credentials

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/drive", tags=["drive_sync"])

# Create a dedicated thread pool for ingestion tasks
ingestion_executor = ThreadPoolExecutor(max_workers=2)

# In-memory storage for sync job status
sync_jobs = {}

@router.get("/sync")
async def sync_drive(
    background_tasks: BackgroundTasks,
    project_id: str = Query(..., description="Project ID to sync files for"),
    user_id: str = Query(..., description="User ID with Google Drive access"),
    max_files: int = Query(100, description="Maximum number of files to sync"),
    mime_types: Optional[str] = Query(None, description="Comma-separated list of MIME types to sync")
):
    """
    Synchronize Google Drive files with RAG system

    This endpoint:
    1. Starts a background task for the heavy processing
    2. Immediately returns a job ID for status tracking
    3. Processes files asynchronously without blocking the UI
    """
    try:
        # Check if user has credentials
        if user_id not in user_credentials:
            return DriveSyncResponse(
                status="error",
                files_indexed=0,
                total_chunks=0,
                error={
                    "message": "User not authenticated with Google Drive",
                    "code": 401
                }
            )

        # Generate a unique job ID
        job_id = f"{project_id}-{datetime.now().isoformat()}"

        # Initialize job status
        sync_jobs[job_id] = {
            "status": "in_progress",
            "project_id": project_id,
            "user_id": user_id,
            "started_at": datetime.now().isoformat(),
            "files_indexed": 0,
            "total_chunks": 0,
            "error": None
        }

        # Start the background task
        background_tasks.add_task(
            run_drive_ingestion,
            job_id,
            project_id,
            user_id,
            max_files,
            mime_types
        )

        # Return immediate response with job ID
        return {
            "status": "syncing_started",
            "job_id": job_id,
            "message": "Drive sync started in background"
        }

    except Exception as e:
        logger.error(f"Error starting Drive sync: {str(e)}")
        return DriveSyncResponse(
            status="error",
            files_indexed=0,
            total_chunks=0,
            error={
                "message": f"Error starting Drive sync: {str(e)}",
                "code": 500
            }
        )

async def run_drive_ingestion(job_id: str, project_id: str, user_id: str, max_files: int, mime_types: Optional[str]):
    """Background task for Drive ingestion"""
    try:
        # Parse mime types if provided
        file_types = mime_types.split(",") if mime_types else None

        # Create Google Drive loader
        drive_loader = GoogleDriveLoader(user_credentials[user_id])

        # Fetch files from Google Drive
        files = await drive_loader.list_files(
            max_results=max_files,
            file_types=file_types
        )

        if not files:
            sync_jobs[job_id] = {
                **sync_jobs[job_id],
                "status": "completed",
                "completed_at": datetime.now().isoformat(),
                "message": "No files found to sync"
            }
            return

        # Update job status with file count
        sync_jobs[job_id]["files_found"] = len(files)

        # Get file IDs
        file_ids = [file["file_id"] for file in files]

        # Load and chunk documents with deduplication
        chunks = await drive_loader.load_and_chunk_documents(
            file_ids=file_ids,
            file_metadata_list=files,
            chunk_size=500,  # ~500 tokens per chunk
            project_id=project_id,
            user_id=user_id
        )

        if not chunks:
            sync_jobs[job_id] = {
                **sync_jobs[job_id],
                "status": "completed",
                "completed_at": datetime.now().isoformat(),
                "message": "No chunks generated from files"
            }
            return

        # Update job status with chunk count
        sync_jobs[job_id]["chunks_generated"] = len(chunks)

        # Run the embedding and storage in a separate thread to avoid blocking
        loop = asyncio.get_event_loop()
        stored_count = await loop.run_in_executor(
            ingestion_executor,
            lambda: asyncio.run(RagService.store_chunks(
                project_id=project_id,
                user_id=user_id,
                chunks=chunks
            ))
        )

        # Update job status with success
        sync_jobs[job_id] = {
            **sync_jobs[job_id],
            "status": "completed",
            "files_indexed": len(set(file["file_id"] for file in files)),
            "total_chunks": stored_count,
            "completed_at": datetime.now().isoformat(),
            "last_synced": datetime.now().isoformat(),
            "message": f"Successfully synced {stored_count} chunks from {len(set(file['file_id'] for file in files))} files"
        }

    except Exception as e:
        logger.error(f"Error in Drive sync background task: {str(e)}")
        sync_jobs[job_id] = {
            **sync_jobs[job_id],
            "status": "failed",
            "error": str(e),
            "completed_at": datetime.now().isoformat()
        }

@router.get("/sync/status")
async def get_sync_status(job_id: str = Query(..., description="Job ID to check status for")):
    """Get the status of a Drive sync job"""
    if job_id not in sync_jobs:
        return {
            "status": "not_found",
            "message": "Sync job not found"
        }

    return sync_jobs[job_id]

@router.get("/status/{project_id}")
async def get_drive_sync_status(project_id: str = Path(..., description="Project ID to get status for")):
    """
    Get the status of Drive sync for a project

    Returns statistics about indexed content
    """
    try:
        # Get stats from RAG service
        stats = await RagService.get_project_stats(project_id)

        # Find the most recent job for this project
        recent_job = None
        for job_id, job in sync_jobs.items():
            if job["project_id"] == project_id:
                if not recent_job or job.get("started_at", "") > recent_job.get("started_at", ""):
                    recent_job = job

        # Combine stats with job info if available
        response = {
            "project_id": stats["project_id"],
            "is_indexed": stats["is_indexed"],
            "total_chunks": stats["total_chunks"],
            "unique_files": stats["unique_files"],
            "last_synced": stats["last_synced"]
        }

        # Add job status if available
        if recent_job:
            response["job_status"] = recent_job.get("status")
            if recent_job.get("status") == "in_progress":
                response["sync_in_progress"] = True

        return response

    except Exception as e:
        logger.error(f"Error getting Drive sync status: {str(e)}")
        return {
            "status": "error",
            "error": {
                "message": f"Error getting Drive sync status: {str(e)}",
                "code": 500
            }
        }