from fastapi import APIRouter, HTTPException, Body, Path
from typing import List, Dict, Any
import logging
from datetime import datetime

from ..schemas.rag import (
    RagIngestRequest, 
    RagIngestResponse, 
    RagQueryRequest, 
    RagQueryResponse, 
    RagQueryResult,
    RagProjectStats
)
from ..services.rag_service import RagService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/rag", tags=["rag"])

@router.post("/ingest")
async def ingest_chunks(request: RagIngestRequest) -> RagIngestResponse:
    """
    Ingest text chunks into the RAG system
    
    This endpoint:
    1. Takes a list of text chunks and their metadata
    2. Generates embeddings for each chunk
    3. Stores them in PostgreSQL with pgvector
    """
    try:
        # Generate embeddings and store in PostgreSQL
        chunks_to_store = [
            {
                "text_chunk": chunk.text_chunk,
                "metadata": chunk.metadata
            }
            for chunk in request.chunks
        ]
        
        # Store chunks
        stored_count = await RagService.store_chunks(
            project_id=request.project_id,
            user_id=request.user_id,
            chunks=chunks_to_store
        )
        
        # Create response
        return RagIngestResponse(
            status="success",
            processed_chunks=stored_count,
            message=f"Successfully ingested {stored_count} chunks",
            last_ingested=datetime.now().isoformat()
        )
        
    except Exception as e:
        logger.error(f"Error in RAG ingest: {str(e)}")
        return RagIngestResponse(
            status="error",
            processed_chunks=0,
            message=f"Error in RAG ingest",
            error={
                "message": str(e),
                "code": 500
            }
        )

@router.post("/query")
async def query_chunks(request: RagQueryRequest) -> RagQueryResponse:
    """
    Query for semantically similar chunks in RAG system
    
    This endpoint:
    1. Takes a query string
    2. Generates an embedding for the query
    3. Searches for similar vectors in PostgreSQL
    4. Returns the most relevant chunks
    """
    try:
        # Query for similar chunks
        results = await RagService.query_chunks(
            project_id=request.project_id,
            query=request.query,
            limit=request.top_k,
            similarity_threshold=request.similarity_threshold
        )
        
        # Convert to response format
        query_results = [
            RagQueryResult(
                id=result["id"],
                text_chunk=result["text_chunk"],
                metadata=result["metadata"],
                similarity=result["similarity"]
            )
            for result in results
        ]
        
        return RagQueryResponse(results=query_results)
        
    except Exception as e:
        logger.error(f"Error in RAG query: {str(e)}")
        return RagQueryResponse(
            results=[],
            error={
                "message": f"Error in RAG query: {str(e)}",
                "code": 500
            }
        )

@router.get("/status/{project_id}")
async def get_rag_status(project_id: str = Path(..., description="Project ID to get status for")) -> RagProjectStats:
    """
    Get RAG status for a project
    
    Returns statistics about indexed content
    """
    try:
        # Get stats from RAG service
        stats = await RagService.get_project_stats(project_id)
        
        return RagProjectStats(
            project_id=stats["project_id"],
            total_chunks=stats["total_chunks"],
            unique_files=stats["unique_files"],
            is_indexed=stats["is_indexed"],
            last_synced=stats["last_synced"]
        )
        
    except Exception as e:
        logger.error(f"Error getting RAG status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting RAG status: {str(e)}"
        )

@router.delete("/project/{project_id}")
async def delete_project_chunks(project_id: str = Path(..., description="Project ID to delete chunks for")):
    """
    Delete all chunks for a project
    
    Removes all RAG data for the specified project
    """
    try:
        # Delete chunks
        deleted_count = await RagService.delete_project_chunks(project_id)
        
        return {
            "status": "success",
            "deleted_chunks": deleted_count,
            "message": f"Successfully deleted {deleted_count} chunks for project {project_id}"
        }
        
    except Exception as e:
        logger.error(f"Error deleting project chunks: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error deleting project chunks: {str(e)}"
        ) 