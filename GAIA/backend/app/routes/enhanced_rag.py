"""
Enhanced RAG API Endpoints for GAIA

This module provides advanced RAG endpoints with:
- Intelligent query processing
- Claude 4 Sonnet integration
- Advanced Google Drive monitoring
- Incremental updates
"""

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime

from ..schemas import (
    PgRagQueryRequest,
    PgRagQueryResponse,
    PgRagStatsResponse
)
from ..services.enhanced_rag_service import EnhancedRagService
from ..services.drive_monitor import GoogleDriveMonitor, SyncResult
from ..utils.google_auth import get_drive_service
from ..routes.auth import user_credentials
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/enhanced-rag", tags=["enhanced-rag"])

# Initialize services
enhanced_rag_service = EnhancedRagService()

# Pydantic models for new endpoints
class EnhancedQueryRequest(BaseModel):
    project_id: str
    user_id: str
    query: str
    max_chunks: Optional[int] = 20
    use_claude: bool = True

class EnhancedQueryResponse(BaseModel):
    response: str
    sources: List[Dict[str, Any]]
    query_analysis: Dict[str, Any]
    context_stats: Dict[str, Any]
    processing_time: float
    error: Optional[str] = None

class DriveMonitorRequest(BaseModel):
    project_id: str
    user_id: str
    force_reindex: bool = False

class DriveMonitorResponse(BaseModel):
    status: str
    sync_result: Dict[str, Any]
    message: str

class DriveStatusResponse(BaseModel):
    project_id: str
    indexed_files: int
    total_chunks: int
    last_sync: Optional[str]
    first_sync: Optional[str]
    file_types: List[Dict[str, Any]]
    error: Optional[str] = None

@router.post("/query", response_model=EnhancedQueryResponse)
async def enhanced_query(request: EnhancedQueryRequest) -> EnhancedQueryResponse:
    """
    Perform enhanced RAG query with Claude 4 Sonnet integration
    
    This endpoint provides:
    - Intelligent query analysis
    - Advanced retrieval strategies
    - Claude 4 Sonnet response generation
    - Comprehensive source attribution
    """
    try:
        logger.info(f"Enhanced RAG query for project {request.project_id}: {request.query}")
        
        # Perform enhanced RAG query
        result = await enhanced_rag_service.enhanced_rag_query(
            project_id=request.project_id,
            query=request.query,
            max_chunks=request.max_chunks
        )
        
        return EnhancedQueryResponse(**result)
    
    except Exception as e:
        logger.error(f"Error in enhanced RAG query: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error in enhanced RAG query: {str(e)}"
        )

@router.post("/drive/sync", response_model=DriveMonitorResponse)
async def sync_drive_files(
    request: DriveMonitorRequest,
    background_tasks: BackgroundTasks
) -> DriveMonitorResponse:
    """
    Perform intelligent Google Drive sync with change detection
    
    This endpoint:
    - Detects new and updated files
    - Performs incremental updates
    - Handles large file collections efficiently
    - Provides detailed sync results
    """
    try:
        # Check if user has credentials
        if request.user_id not in user_credentials:
            raise HTTPException(
                status_code=401, 
                detail="User not authenticated with Google Drive"
            )
        
        # Initialize Drive monitor
        drive_monitor = GoogleDriveMonitor(user_credentials[request.user_id])
        
        # Perform sync
        logger.info(f"Starting Drive sync for project {request.project_id}")
        sync_result = await drive_monitor.sync_files(
            project_id=request.project_id,
            user_id=request.user_id,
            force_reindex=request.force_reindex
        )
        
        # Convert SyncResult to dict for response
        sync_result_dict = {
            "processed_files": sync_result.processed_files,
            "new_files": sync_result.new_files,
            "updated_files": sync_result.updated_files,
            "total_chunks": sync_result.total_chunks,
            "errors": sync_result.errors,
            "sync_time": sync_result.sync_time
        }
        
        message = f"Sync completed: {sync_result.processed_files} files processed, {sync_result.total_chunks} chunks indexed"
        if sync_result.errors:
            message += f" ({len(sync_result.errors)} errors)"
        
        return DriveMonitorResponse(
            status="success",
            sync_result=sync_result_dict,
            message=message
        )
    
    except Exception as e:
        logger.error(f"Error in Drive sync: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error in Drive sync: {str(e)}"
        )

@router.get("/drive/status/{project_id}", response_model=DriveStatusResponse)
async def get_drive_status(project_id: str, user_id: str) -> DriveStatusResponse:
    """
    Get detailed Google Drive sync status for a project
    
    Returns:
    - Number of indexed files
    - Total chunks
    - Last sync time
    - File type breakdown
    """
    try:
        # Check if user has credentials
        if user_id not in user_credentials:
            raise HTTPException(
                status_code=401, 
                detail="User not authenticated with Google Drive"
            )
        
        # Initialize Drive monitor
        drive_monitor = GoogleDriveMonitor(user_credentials[user_id])
        
        # Get status
        status = await drive_monitor.get_sync_status(project_id)
        
        return DriveStatusResponse(**status)
    
    except Exception as e:
        logger.error(f"Error getting Drive status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting Drive status: {str(e)}"
        )

@router.post("/drive/detect-changes")
async def detect_drive_changes(project_id: str, user_id: str) -> Dict[str, Any]:
    """
    Detect changes in Google Drive without performing sync
    
    Returns information about new, updated, and unchanged files
    """
    try:
        # Check if user has credentials
        if user_id not in user_credentials:
            raise HTTPException(
                status_code=401, 
                detail="User not authenticated with Google Drive"
            )
        
        # Initialize Drive monitor
        drive_monitor = GoogleDriveMonitor(user_credentials[user_id])
        
        # Detect changes
        changes = await drive_monitor.detect_changes(project_id)
        
        return {
            "project_id": project_id,
            "new_files_count": len(changes['new_files']),
            "updated_files_count": len(changes['updated_files']),
            "unchanged_files_count": len(changes['unchanged_files']),
            "new_files": [
                {
                    "id": f["id"],
                    "name": f["name"],
                    "modified_time": f["modifiedTime"]
                }
                for f in changes['new_files'][:10]  # Limit to first 10
            ],
            "updated_files": [
                {
                    "id": f["id"],
                    "name": f["name"],
                    "modified_time": f["modifiedTime"]
                }
                for f in changes['updated_files'][:10]  # Limit to first 10
            ],
            "requires_sync": len(changes['new_files']) > 0 or len(changes['updated_files']) > 0
        }
    
    except Exception as e:
        logger.error(f"Error detecting Drive changes: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error detecting Drive changes: {str(e)}"
        )

@router.get("/analytics/{project_id}")
async def get_rag_analytics(project_id: str) -> Dict[str, Any]:
    """
    Get comprehensive RAG analytics for a project
    
    Returns:
    - Document statistics
    - Query performance metrics
    - Source distribution
    - Recent activity
    """
    try:
        from ..utils.db_utils import get_db_connection
        
        conn = await get_db_connection()
        
        try:
            # Get overall statistics
            stats = await conn.fetchrow("""
                SELECT 
                    COUNT(*) as total_chunks,
                    COUNT(DISTINCT metadata->>'file_id') as unique_files,
                    COUNT(DISTINCT metadata->>'source') as source_types,
                    AVG(LENGTH(text_chunk)) as avg_chunk_length,
                    MIN(created_at) as first_indexed,
                    MAX(created_at) as last_indexed
                FROM embeddings 
                WHERE project_id = $1
            """, project_id)
            
            # Get source breakdown
            source_breakdown = await conn.fetch("""
                SELECT 
                    metadata->>'source' as source,
                    COUNT(*) as chunk_count,
                    COUNT(DISTINCT metadata->>'file_id') as file_count
                FROM embeddings 
                WHERE project_id = $1
                GROUP BY metadata->>'source'
                ORDER BY chunk_count DESC
            """, project_id)
            
            # Get file type breakdown
            file_type_breakdown = await conn.fetch("""
                SELECT 
                    metadata->>'mime_type' as mime_type,
                    COUNT(*) as chunk_count,
                    COUNT(DISTINCT metadata->>'file_id') as file_count
                FROM embeddings 
                WHERE project_id = $1
                GROUP BY metadata->>'mime_type'
                ORDER BY chunk_count DESC
            """, project_id)
            
            # Get recent activity (last 7 days)
            recent_activity = await conn.fetch("""
                SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as chunks_added,
                    COUNT(DISTINCT metadata->>'file_id') as files_added
                FROM embeddings 
                WHERE project_id = $1 
                  AND created_at >= NOW() - INTERVAL '7 days'
                GROUP BY DATE(created_at)
                ORDER BY date DESC
            """, project_id)
            
            return {
                "project_id": project_id,
                "overview": {
                    "total_chunks": stats['total_chunks'] if stats else 0,
                    "unique_files": stats['unique_files'] if stats else 0,
                    "source_types": stats['source_types'] if stats else 0,
                    "avg_chunk_length": float(stats['avg_chunk_length']) if stats and stats['avg_chunk_length'] else 0,
                    "first_indexed": stats['first_indexed'].isoformat() if stats and stats['first_indexed'] else None,
                    "last_indexed": stats['last_indexed'].isoformat() if stats and stats['last_indexed'] else None
                },
                "source_breakdown": [
                    {
                        "source": row['source'],
                        "chunk_count": row['chunk_count'],
                        "file_count": row['file_count']
                    }
                    for row in source_breakdown
                ],
                "file_type_breakdown": [
                    {
                        "mime_type": row['mime_type'],
                        "chunk_count": row['chunk_count'],
                        "file_count": row['file_count']
                    }
                    for row in file_type_breakdown
                ],
                "recent_activity": [
                    {
                        "date": row['date'].isoformat(),
                        "chunks_added": row['chunks_added'],
                        "files_added": row['files_added']
                    }
                    for row in recent_activity
                ]
            }
            
        finally:
            await conn.close()
    
    except Exception as e:
        logger.error(f"Error getting RAG analytics: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting RAG analytics: {str(e)}"
        )
