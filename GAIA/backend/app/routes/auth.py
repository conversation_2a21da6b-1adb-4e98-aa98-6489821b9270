from fastapi import APIRouter, Depends, HTTPException, Request, Query, Response
from fastapi.responses import RedirectResponse
import os
from typing import Dict, Optional
import json
from datetime import datetime, timedelta
from ..utils.google_auth import get_authorization_url, get_credentials_from_code
from ..utils.db_utils import (
    store_user_credentials,
    load_user_credentials,
    load_all_user_credentials,
    delete_user_credentials
)
import logging
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/auth", tags=["auth"])

# In-memory storage for OAuth states and user credentials
# In production, this would be stored in a database
oauth_states = {}
user_credentials = {}

@router.get("/google/login")
async def login_google(response: Response, project_id: Optional[str] = None):
    """
    Initiate Google OAuth flow by redirecting to Google's consent page
    """
    try:
        # Generate a state token to prevent CSRF
        state = str(uuid.uuid4())

        # Store state with project context if provided
        context = {}
        if project_id:
            context["project_id"] = project_id

        # Store state and context
        oauth_states[state] = context

        # Get authorization URL without state parameter
        auth_url, _ = get_authorization_url()

        # Add state parameter to URL in a valid format
        if '?' in auth_url:
            auth_url = f"{auth_url}&state={state}"
        else:
            auth_url = f"{auth_url}?state={state}"

        logger.info(f"Redirecting to Google OAuth: {auth_url}")
        return RedirectResponse(url=auth_url)

    except Exception as e:
        logger.error(f"Error initiating Google login: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error initiating Google login: {str(e)}")

@router.get("/google/callback")
async def auth_callback(code: str = Query(...), state: Optional[str] = Query(None)):
    """
    Handle callback from Google OAuth with authorization code
    """
    try:
        # Verify state parameter to prevent CSRF
        if state not in oauth_states:
            raise HTTPException(status_code=400, detail="Invalid state parameter")

        # Get stored context
        context = oauth_states.pop(state)

        # Exchange code for credentials
        credentials_dict = get_credentials_from_code(code)

        # Generate a user ID (in production, this would be from the actual user)
        user_id = str(uuid.uuid4())

        # Store credentials in memory for immediate use
        user_credentials[user_id] = credentials_dict

        # Store credentials persistently in database
        await store_user_credentials(user_id, credentials_dict)
        logger.info(f"Stored persistent credentials for user: {user_id}")

        # Construct redirect URL with context
        redirect_url = "http://localhost:3000"  # Frontend URL

        if "project_id" in context:
            redirect_url += f"?project_id={context['project_id']}"
            redirect_url += f"&auth_success=true"
            redirect_url += f"&user_id={user_id}"

        return RedirectResponse(url=redirect_url)

    except Exception as e:
        logger.error(f"Error in Google callback: {str(e)}")
        return RedirectResponse(url=f"http://localhost:3000?error={str(e)}")

@router.get("/google/credentials/{user_id}")
async def get_user_credentials(user_id: str):
    """
    Get stored credentials for a user by ID
    In production, this would check authentication and only return the user's own credentials
    """
    logger.info(f"Checking credentials for user_id: {user_id}")
    logger.info(f"Available user_ids in memory: {list(user_credentials.keys())}")

    credentials = None

    # First check in-memory storage
    if user_id in user_credentials:
        credentials = user_credentials[user_id]
        logger.info(f"Found credentials in memory for user_id: {user_id}")
    else:
        # If not in memory, try loading from database
        logger.info(f"Credentials not in memory, loading from database for user_id: {user_id}")
        credentials = await load_user_credentials(user_id)

        if credentials:
            # Store in memory for future requests
            user_credentials[user_id] = credentials
            logger.info(f"Loaded and cached credentials from database for user_id: {user_id}")
        else:
            logger.warning(f"User credentials not found in database for user_id: {user_id}")
            raise HTTPException(status_code=404, detail=f"User credentials not found for user_id: {user_id}")

    # Check if credentials are expired and need refresh
    try:
        # Basic validation - check if we have required fields
        if not credentials.get("access_token"):
            logger.warning(f"Missing access_token for user_id: {user_id}")
            raise HTTPException(status_code=401, detail="Invalid credentials - missing access token")

        # For security, we return a sanitized version without the actual tokens
        response = {
            "user_id": user_id,
            "has_credentials": True,
            "scopes": credentials.get("scopes", []),
            "token_type": credentials.get("token_type", "Bearer")
        }

        logger.info(f"Successfully retrieved credentials for user_id: {user_id}")
        return response

    except Exception as e:
        logger.error(f"Error validating credentials for user_id {user_id}: {str(e)}")
        raise HTTPException(status_code=401, detail=f"Invalid or expired credentials: {str(e)}")

@router.get("/debug/users")
async def debug_list_users():
    """
    Debug endpoint to list all stored user credentials
    """
    # Also check database
    try:
        db_credentials = await load_all_user_credentials()
        db_user_count = len(db_credentials)

        # Check for users with missing access_token
        memory_users_with_token = []
        memory_users_without_token = []

        for user_id, creds in user_credentials.items():
            if creds.get("access_token"):
                memory_users_with_token.append(user_id)
            else:
                memory_users_without_token.append(user_id)

        return {
            "memory": {
                "total_users": len(user_credentials),
                "user_ids": list(user_credentials.keys()),
                "users_with_access_token": memory_users_with_token,
                "users_without_access_token": memory_users_without_token
            },
            "database": {
                "total_users": db_user_count,
                "user_ids": list(db_credentials.keys()) if db_credentials else []
            },
            "oauth_states": len(oauth_states)
        }
    except Exception as e:
        return {
            "memory": {
                "total_users": len(user_credentials),
                "user_ids": list(user_credentials.keys()),
                "error": f"Could not check database: {str(e)}"
            },
            "oauth_states": len(oauth_states)
        }

@router.post("/debug/test-credentials-format")
async def test_credentials_format():
    """
    Debug endpoint to test credentials format from OAuth
    """
    try:
        from ..utils.google_auth import get_credentials_from_code

        # This will fail with mock code but shows the expected format
        try:
            result = get_credentials_from_code("mock_code_for_testing")
            return {"status": "unexpected_success", "format": result}
        except Exception as e:
            # Expected to fail, but we can see what fields would be returned
            return {
                "status": "expected_error",
                "error": str(e),
                "expected_format": {
                    "access_token": "string (from credentials.token)",
                    "refresh_token": "string (from credentials.refresh_token)",
                    "token_type": "Bearer",
                    "scopes": ["list", "of", "scopes"],
                    "expires_at": "datetime or null"
                }
            }
    except Exception as e:
        return {"status": "error", "error": str(e)}

@router.post("/google/revoke/{user_id}")
async def revoke_google_auth(user_id: str):
    """
    Revoke Google authentication tokens
    """
    try:
        # Check if user credentials exist in memory or database
        credentials_exist = user_id in user_credentials
        if not credentials_exist:
            # Check database
            db_credentials = await load_user_credentials(user_id)
            credentials_exist = db_credentials is not None

        if not credentials_exist:
            raise HTTPException(status_code=404, detail="User credentials not found")

        # In a production app, you would use Google's revoke endpoint:
        # https://oauth2.googleapis.com/revoke?token={token}

        # Remove credentials from memory if present
        if user_id in user_credentials:
            del user_credentials[user_id]
            logger.info(f"Removed credentials from memory for user: {user_id}")

        # Remove credentials from database
        deleted = await delete_user_credentials(user_id)
        if deleted:
            logger.info(f"Removed credentials from database for user: {user_id}")

        return {"status": "success", "message": "Google authentication revoked"}

    except Exception as e:
        logger.error(f"Error revoking Google auth: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error revoking Google auth: {str(e)}")