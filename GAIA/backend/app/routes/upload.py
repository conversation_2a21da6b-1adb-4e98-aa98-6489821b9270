from fastapi import APIRouter, UploadFile, File, Form, HTTPException
from typing import List, Optional
import logging
import os
import tempfile
import uuid
from ..utils.rag_utils import chunk_text
from ..utils.db_utils import store_embeddings
from ..schemas import PgRagIngestResponse
import io
from PyPDF2 import PdfReader
import docx

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/upload", tags=["upload"])

def extract_text_from_pdf(temp_path):
    """Extract text from PDF file using the file path"""
    text = ""
    try:
        with open(temp_path, "rb") as file:
            pdf_reader = PdfReader(file)
            for page in pdf_reader.pages:
                text += page.extract_text() + "\n"
    except Exception as e:
        logger.error(f"Error extracting text from PDF: {str(e)}")
    return text

def extract_text_from_docx(temp_path):
    """Extract text from DOCX file using the file path"""
    text = ""
    try:
        doc = docx.Document(temp_path)
        for para in doc.paragraphs:
            text += para.text + "\n"
    except Exception as e:
        logger.error(f"Error extracting text from DOCX: {str(e)}")
    return text

def extract_text_from_txt(temp_path):
    """Extract text from text file using the file path"""
    try:
        with open(temp_path, "r", encoding="utf-8", errors="ignore") as file:
            return file.read()
    except Exception as e:
        logger.error(f"Error extracting text from text file: {str(e)}")
        return ""

# Define local chunking function as fallback in case import fails
def local_chunk_text(text: str, max_tokens: int = 400):
    """Split text into chunks of roughly max_tokens size"""
    sentences = text.replace('\n', ' ').split('. ')
    chunks = []
    current_chunk = ""
    
    for sentence in sentences:
        sentence_tokens = len(sentence) / 4
        current_chunk_tokens = len(current_chunk) / 4
        
        if current_chunk_tokens + sentence_tokens > max_tokens and current_chunk:
            chunks.append(current_chunk.strip())
            current_chunk = sentence + ". "
        else:
            current_chunk += sentence + ". "
    
    if current_chunk:
        chunks.append(current_chunk.strip())
        
    return chunks

@router.post("/files")
async def upload_files(
    files: List[UploadFile] = File(...),
    project_id: str = Form(...),
    user_id: Optional[str] = Form("anonymous")
) -> PgRagIngestResponse:
    """
    Upload files and process them for RAG
    
    This endpoint:
    1. Accepts multiple file uploads
    2. Extracts text from the files
    3. Chunks the text
    4. Generates embeddings
    5. Stores the embeddings in PostgreSQL
    """
    try:
        processed_files = 0
        total_chunks = 0
        
        for file in files:
            try:
                # Get file content
                content = await file.read()
                file_name = file.filename or f"unnamed_{uuid.uuid4()}"
                mime_type = file.content_type or "application/octet-stream"
                
                logger.info(f"Processing file: {file_name}, mime type: {mime_type}")
                
                # Create temporary file to handle different formats properly
                with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file_name)[1]) as temp_file:
                    temp_file.write(content)
                    temp_path = temp_file.name
                
                try:
                    # Extract text based on file type
                    text = ""
                    if "pdf" in mime_type or file_name.lower().endswith(".pdf"):
                        logger.info(f"Processing as PDF: {file_name}")
                        text = extract_text_from_pdf(temp_path)
                    elif "document" in mime_type or file_name.lower().endswith((".doc", ".docx")):
                        logger.info(f"Processing as DOCX: {file_name}")
                        text = extract_text_from_docx(temp_path)
                    elif "text/plain" in mime_type or file_name.lower().endswith((".txt", ".text")):
                        logger.info(f"Processing as TXT: {file_name}")
                        text = extract_text_from_txt(temp_path)
                    else:
                        logger.warning(f"Unsupported file type: {mime_type}, filename: {file_name}")
                        continue
                    
                    # Log extraction results
                    if text:
                        text_preview = text[:100] + "..." if len(text) > 100 else text
                        logger.info(f"Extracted text from {file_name}: {text_preview}")
                    else:
                        logger.warning(f"No text extracted from file: {file_name}")
                        continue
                    
                    # Try imported chunk_text first, fallback to local version if needed
                    try:
                        chunks = chunk_text(text)
                        logger.info(f"Using imported chunk_text function")
                    except Exception as e:
                        logger.warning(f"Error using imported chunk_text: {str(e)}, falling back to local version")
                        chunks = local_chunk_text(text)
                    
                    if not chunks:
                        logger.warning(f"No chunks generated from file: {file_name}")
                        continue
                    
                    logger.info(f"Generated {len(chunks)} chunks from {file_name}")
                    
                    # Prepare chunks with metadata for storage
                    db_chunks = []
                    for i, chunk_text_content in enumerate(chunks):
                        metadata = {
                            "file_id": str(uuid.uuid4()),  # Generate a unique ID for uploaded file
                            "file_name": file_name,
                            "mime_type": mime_type,
                            "chunk_index": i,
                            "modified_time": "",  # No timestamp for uploaded files
                            "source": "upload",  # Mark as uploaded vs Google Drive
                        }
                        
                        db_chunks.append({
                            "text_chunk": chunk_text_content,
                            "metadata": metadata
                        })
                    
                    # Store in PostgreSQL
                    stored_count = await store_embeddings(
                        project_id=project_id,
                        user_id=user_id,
                        chunks=db_chunks
                    )
                    
                    processed_files += 1
                    total_chunks += stored_count
                    
                    logger.info(f"Processed file {file_name}: {stored_count} chunks")
                    
                finally:
                    # Clean up temporary file
                    try:
                        os.unlink(temp_path)
                    except Exception as e:
                        logger.error(f"Error removing temp file {temp_path}: {str(e)}")
                    
            except Exception as e:
                logger.error(f"Error processing file {file.filename}: {str(e)}")
                continue
        
        return PgRagIngestResponse(
            status="success",
            processed_chunks=total_chunks,
            message=f"Successfully processed {processed_files} files with {total_chunks} chunks"
        )
        
    except Exception as e:
        logger.error(f"Error in file upload: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing files: {str(e)}") 