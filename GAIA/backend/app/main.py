from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from .routes import chat, auth, drive, pgrag as rag, upload, drive_sync, rag_endpoints, enhanced_rag
from .utils.db_utils import init_db, load_all_user_credentials
from dotenv import load_dotenv
import logging

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("🚀 Starting GAIA backend...")

    # Initialize database (creates tables including user_credentials)
    await init_db()
    logger.info("✅ Database initialized")

    # Initialize RAG system
    await rag.initialize_db()
    logger.info("✅ RAG system initialized")

    # Load persistent user credentials into memory
    try:
        persistent_credentials = await load_all_user_credentials()
        # Update the auth module's in-memory storage
        auth.user_credentials.update(persistent_credentials)
        logger.info(f"✅ Loaded {len(persistent_credentials)} user credentials from database")
    except Exception as e:
        logger.warning(f"⚠️ Could not load user credentials: {str(e)}")

    logger.info("🎉 GAIA backend startup complete!")

    yield

    # Shutdown
    logger.info("🛑 GAIA backend shutting down...")
    # Note: Credentials are automatically persisted to database during operation
    logger.info("✅ Shutdown complete")

# Create FastAPI app with lifespan
app = FastAPI(
    title="GAIA API",
    description="GPT AI Assistant API for enterprise knowledge workers with advanced RAG capabilities",
    version="2.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # React development server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(chat.router)
app.include_router(auth.router)
app.include_router(drive.router)  # Original Drive endpoints
app.include_router(drive_sync.router)  # New Drive sync endpoints
app.include_router(rag.router, prefix="/api/rag", tags=["rag"])  # PostgreSQL RAG as main RAG system
app.include_router(rag_endpoints.router)  # New RAG endpoints
app.include_router(enhanced_rag.router)  # Enhanced RAG with Claude integration
app.include_router(upload.router)  # File upload handler

# Root endpoint
@app.get("/")
async def root():
    return {"message": "Welcome to GAIA API"}

# Health check endpoint
@app.get("/health")
async def health():
    return {"status": "ok"}