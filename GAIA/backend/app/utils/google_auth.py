import os
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from dotenv import load_dotenv
import urllib.parse

# Load environment variables
load_dotenv()

# Google OAuth configuration
CLIENT_ID = os.getenv("GOOGLE_CLIENT_ID")
CLIENT_SECRET = os.getenv("GOOGLE_CLIENT_SECRET")
REDIRECT_URI = os.getenv("OAUTH_REDIRECT_URI")
SCOPES = [
    "https://www.googleapis.com/auth/drive.readonly",
    "https://www.googleapis.com/auth/drive.metadata.readonly"
]

def create_oauth_flow():
    """Create and return a Google OAuth2 flow object"""
    flow = Flow.from_client_config(
        {
            "web": {
                "client_id": CLIENT_ID,
                "client_secret": CLIENT_SECRET,
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "redirect_uris": [REDIRECT_URI]
            }
        },
        scopes=SCOPES
    )
    flow.redirect_uri = REDIRECT_URI
    return flow

def get_drive_service(credentials_dict):
    """Build and return a Drive API service object using the provided credentials"""
    # Handle both old format ("token") and new format ("access_token")
    access_token = credentials_dict.get("access_token") or credentials_dict.get("token")

    credentials = Credentials(
        token=access_token,
        refresh_token=credentials_dict.get("refresh_token"),
        token_uri=credentials_dict.get("token_uri", "https://oauth2.googleapis.com/token"),
        client_id=CLIENT_ID,
        client_secret=CLIENT_SECRET,
        scopes=credentials_dict.get("scopes", SCOPES)
    )

    return build('drive', 'v3', credentials=credentials)

def get_authorization_url():
    """Get the Google OAuth authorization URL"""
    flow = create_oauth_flow()
    # Generate the authorization URL without state parameter
    # The state parameter will be appended separately by the auth route
    authorization_url, state = flow.authorization_url(
        access_type='offline',
        include_granted_scopes='true',
        prompt='consent'
    )

    # Parse and return the URL without state to prevent double state parameters
    parsed_url = urllib.parse.urlparse(authorization_url)
    query_params = urllib.parse.parse_qs(parsed_url.query)

    # Remove state from the original query parameters
    if 'state' in query_params:
        del query_params['state']

    # Rebuild URL without state
    new_query = urllib.parse.urlencode(query_params, doseq=True)
    parsed_url = parsed_url._replace(query=new_query)
    clean_authorization_url = urllib.parse.urlunparse(parsed_url)

    return clean_authorization_url, state

def get_credentials_from_code(code):
    """Exchange authorization code for credentials"""
    flow = create_oauth_flow()
    flow.fetch_token(code=code)
    credentials = flow.credentials

    # Create a dict with the credentials info to store
    # Map to the expected field names for our database schema
    return {
        "access_token": credentials.token,  # Map 'token' to 'access_token'
        "refresh_token": credentials.refresh_token,
        "token_type": "Bearer",  # Standard OAuth2 token type
        "token_uri": credentials.token_uri,
        "client_id": credentials.client_id,
        "scopes": credentials.scopes,
        "expires_at": credentials.expiry  # Include expiration time
    }