import io
import docx
from PyPDF2 import PdfReader
from openai import OpenAI
from googleapiclient.http import MediaIoBaseDownload
from dotenv import load_dotenv
import logging
from typing import List

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def extract_text_from_file(file_content, mime_type):
    """Extract text from various file types"""
    text = ""

    if "pdf" in mime_type:
        # Handle PDF files
        pdf_reader = PdfReader(io.BytesIO(file_content))
        for page in pdf_reader.pages:
            text += page.extract_text() + "\n"

    elif "document" in mime_type:
        # Handle DOCX files
        doc = docx.Document(io.BytesIO(file_content))
        for para in doc.paragraphs:
            text += para.text + "\n"

    elif "text/plain" in mime_type or "text/csv" in mime_type:
        # Handle text files with UTF-8 fallback
        try:
            text = file_content.decode("utf-8")
        except UnicodeDecodeError:
            logger.warning("UTF-8 decode failed, trying latin-1")
            try:
                text = file_content.decode("latin-1")
            except UnicodeDecodeError:
                logger.warning("Latin-1 decode failed, using error replacement")
                text = file_content.decode("utf-8", errors="replace")

    return text

def chunk_text(text: str, max_tokens: int = 400):
    """Split text into chunks of roughly max_tokens size"""
    sentences = text.replace('\n', ' ').split('. ')
    chunks = []
    current_chunk = ""

    for sentence in sentences:
        # Rough approximation of token count (4 chars = ~1 token)
        sentence_tokens = len(sentence) / 4
        current_chunk_tokens = len(current_chunk) / 4

        if current_chunk_tokens + sentence_tokens > max_tokens and current_chunk:
            chunks.append(current_chunk.strip())
            current_chunk = sentence + ". "
        else:
            current_chunk += sentence + ". "

    if current_chunk:
        chunks.append(current_chunk.strip())

    return chunks

def download_file_from_drive(drive_service, file_id):
    """Download a file from Google Drive"""
    request = drive_service.files().get_media(fileId=file_id)
    file_content = io.BytesIO()
    downloader = MediaIoBaseDownload(file_content, request)

    done = False
    while not done:
        status, done = downloader.next_chunk()

    return file_content.getvalue()

# This function now works with the non-async OpenAI client
def get_embeddings_for_text(texts: List[str]):
    """
    Generate embeddings for text chunks using OpenAI API
    This is a non-async wrapper for multiple texts
    """
    results = []
    client = OpenAI()  # Reads OPENAI_API_KEY from environment automatically

    for text in texts:
        try:
            response = client.embeddings.create(
                model="text-embedding-3-small",
                input=text
            )
            results.append(response.data[0].embedding)
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            # Return a dummy embedding of correct dimensionality to avoid crashing
            results.append([0.0] * 1536)  # text-embedding-3-small has 1536 dimensions

    return results