import os
import json
import asyncpg
from openai import OpenAI
from typing import List, Dict, Any, Optional
import logging
from dotenv import load_dotenv
import asyncio

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database connection string
DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/gaia")

# Custom vector type handling for asyncpg
async def prepare_database():
    """Set up asyncpg connection with vector support"""
    # Create a connection
    conn = await asyncpg.connect(DATABASE_URL)

    # Create a custom type codec for vectors
    await conn.execute("CREATE EXTENSION IF NOT EXISTS vector")

    # Define a custom codec for the vector type
    # This transforms Python lists to PostgreSQL vectors and back
    await conn.set_type_codec(
        'vector',
        encoder=lambda vector: f"[{','.join(map(str, vector))}]",
        decoder=lambda value: [float(x) for x in value[1:-1].split(',')],
        schema='public',
        format='text'
    )

    await conn.close()

# Database initialization
async def init_db():
    """Initialize the database with pgvector extension and tables"""
    # First prepare the database with custom vector handling
    await prepare_database()

    conn = await asyncpg.connect(DATABASE_URL)

    try:
        # Create pgvector extension if not exists
        await conn.execute("CREATE EXTENSION IF NOT EXISTS vector")

        # Create embeddings table if not exists
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS embeddings (
                id SERIAL PRIMARY KEY,
                project_id VARCHAR(255) NOT NULL,
                user_id VARCHAR(255) NOT NULL,
                text_chunk TEXT NOT NULL,
                embedding vector(1536) NOT NULL,
                metadata JSONB NOT NULL,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Create user_credentials table for persistent OAuth storage
        await conn.execute("""
            CREATE TABLE IF NOT EXISTS user_credentials (
                user_id VARCHAR(255) PRIMARY KEY,
                access_token TEXT NOT NULL,
                refresh_token TEXT,
                token_type VARCHAR(50) DEFAULT 'Bearer',
                scopes TEXT[],
                expires_at TIMESTAMP WITH TIME ZONE,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
        """)

        # Create efficient vector search index
        try:
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS embeddings_embedding_idx
                ON embeddings USING ivfflat (embedding vector_cosine_ops)
                WITH (lists = 100)
            """)
            logger.info("IVFFlat vector index created successfully")
        except Exception as e:
            logger.warning(f"Could not create IVFFlat index (may require data): {str(e)}")
            # Create basic index just in case
            await conn.execute("""
                CREATE INDEX IF NOT EXISTS embeddings_project_id_idx
                ON embeddings(project_id)
            """)

        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise
    finally:
        await conn.close()

async def get_embedding(text: str, executor=None) -> List[float]:
    """
    Get embedding from OpenAI API

    Parameters:
    - text: Text to generate embedding for
    - executor: Optional ThreadPoolExecutor to use for running the embedding task
    """
    try:
        # Create OpenAI client - reads OPENAI_API_KEY from environment automatically
        client = OpenAI()

        # If executor is provided, run in the executor
        if executor:
            loop = asyncio.get_event_loop()
            response = await loop.run_in_executor(
                executor,
                lambda: client.embeddings.create(
                    model="text-embedding-3-small",
                    input=text
                )
            )
        else:
            # Call the embedding API without await (it's not an async call with the new client)
            response = client.embeddings.create(
                model="text-embedding-3-small",
                input=text
            )

        return response.data[0].embedding
    except Exception as e:
        logger.error(f"Error getting embedding: {str(e)}")
        raise

async def get_db_connection():
    """Get a database connection with vector support"""
    conn = await asyncpg.connect(DATABASE_URL)

    # Set up vector codec for this connection
    await conn.set_type_codec(
        'vector',
        encoder=lambda vector: f"[{','.join(map(str, vector))}]",
        decoder=lambda value: [float(x) for x in value[1:-1].split(',')],
        schema='public',
        format='text'
    )

    return conn

async def store_embeddings(
    project_id: str,
    user_id: str,
    chunks: List[Dict[str, Any]]
) -> int:
    """Store text chunks and their embeddings in the database"""
    conn = await get_db_connection()
    stored_count = 0

    try:
        # Process each chunk
        for chunk in chunks:
            text_chunk = chunk["text_chunk"]
            metadata = chunk["metadata"]

            # Get embedding
            embedding = await get_embedding(text_chunk)

            # Store in database - with proper vector format
            await conn.execute("""
                INSERT INTO embeddings (project_id, user_id, text_chunk, embedding, metadata)
                VALUES ($1, $2, $3, $4, $5)
            """, project_id, user_id, text_chunk, embedding, json.dumps(metadata))

            stored_count += 1

        return stored_count
    except Exception as e:
        logger.error(f"Error storing embeddings: {str(e)}")
        raise
    finally:
        await conn.close()

async def query_embeddings(
    project_id: str,
    query: str,
    user_id: str = None,
    source_filter: str = None,  # 'google_drive', 'upload', or None for all
    limit: int = 5,
    similarity_threshold: float = 0.5
) -> List[Dict[str, Any]]:
    """Query embeddings by semantic similarity with filtering options"""
    conn = await get_db_connection()

    try:
        # Get embedding for query
        query_embedding = await get_embedding(query)

        # Build WHERE clause based on filters
        where_conditions = ["project_id = $2"]
        where_params = [query_embedding, project_id]
        param_count = 2

        # Add similarity threshold filter
        param_count += 1
        where_conditions.append(f"1 - (embedding <=> $1::vector) >= ${param_count}")
        where_params.append(similarity_threshold)

        # Add source filter
        if source_filter == 'google_drive':
            param_count += 1
            where_conditions.append(f"metadata->>'source' = ${param_count}")
            where_params.append('google_drive')

            # For Google Drive files, also filter by user_id
            if user_id:
                param_count += 1
                where_conditions.append(f"user_id = ${param_count}")
                where_params.append(user_id)

        elif source_filter == 'upload':
            param_count += 1
            where_conditions.append(f"metadata->>'source' = ${param_count}")
            where_params.append('upload')

        elif source_filter is None:
            # Handle both Google Drive and uploaded files
            drive_cond = f"(metadata->>'source' = 'google_drive'"
            if user_id:
                param_count += 1
                drive_cond += f" AND user_id = ${param_count}"
                where_params.append(user_id)
            drive_cond += ")"

            param_count += 1
            upload_cond = f"(metadata->>'source' = ${param_count})"
            where_params.append('upload')

            where_conditions.append(f"({drive_cond} OR {upload_cond})")

        # Build final query
        where_clause = " AND ".join(where_conditions)
        query_sql = f"""
            SELECT id, project_id, user_id, text_chunk, metadata,
                   1 - (embedding <=> $1::vector) AS similarity
            FROM embeddings
            WHERE {where_clause}
            ORDER BY similarity DESC
            LIMIT {limit}
        """

        # Execute query
        rows = await conn.fetch(query_sql, *where_params)

        # Format results
        results = []
        for row in rows:
            metadata = json.loads(row["metadata"])
            # Extract file_name from metadata based on source
            file_name = metadata.get('file_name') or metadata.get('name', 'Unknown File')

            results.append({
                "id": row["id"],
                "project_id": row["project_id"],
                "user_id": row["user_id"],
                "text_chunk": row["text_chunk"],
                "metadata": metadata,
                "similarity": float(row["similarity"]),
                "file_name": file_name
            })

        logger.info(f"Found {len(results)} chunks for query with source_filter='{source_filter}', similarity_threshold={similarity_threshold}")
        return results

    except Exception as e:
        logger.error(f"Error querying embeddings: {str(e)}")
        raise
    finally:
        await conn.close()

async def delete_project_embeddings(project_id: str) -> int:
    """Delete all embeddings for a specific project"""
    conn = await asyncpg.connect(DATABASE_URL)

    try:
        result = await conn.execute("""
            DELETE FROM embeddings
            WHERE project_id = $1
        """, project_id)

        # Parse the DELETE count from result string like "DELETE 5"
        count = int(result.split()[1]) if result else 0
        return count
    except Exception as e:
        logger.error(f"Error deleting project embeddings: {str(e)}")
        raise
    finally:
        await conn.close()

async def get_project_stats(project_id: str) -> Dict[str, Any]:
    """Get statistics about embeddings for a project"""
    conn = await asyncpg.connect(DATABASE_URL)

    try:
        # Get count of embeddings
        row = await conn.fetchrow("""
            SELECT
                COUNT(*) as total_chunks,
                COUNT(DISTINCT metadata->>'file_id') as unique_files
            FROM embeddings
            WHERE project_id = $1
        """, project_id)

        return {
            "project_id": project_id,
            "total_chunks": row["total_chunks"],
            "unique_files": row["unique_files"],
            "is_indexed": row["total_chunks"] > 0
        }
    except Exception as e:
        logger.error(f"Error getting project stats: {str(e)}")
        raise
    finally:
        await conn.close()

# Persistent Credentials Management
async def store_user_credentials(user_id: str, credentials: Dict[str, Any]) -> bool:
    """Store user credentials in the database"""
    conn = await asyncpg.connect(DATABASE_URL)

    try:
        # Extract credentials data
        access_token = credentials.get("access_token")
        refresh_token = credentials.get("refresh_token")
        token_type = credentials.get("token_type", "Bearer")
        scopes = credentials.get("scopes", [])
        expires_at = credentials.get("expires_at")  # Should be a datetime object

        # Insert or update credentials
        await conn.execute("""
            INSERT INTO user_credentials (user_id, access_token, refresh_token, token_type, scopes, expires_at, updated_at)
            VALUES ($1, $2, $3, $4, $5, $6, CURRENT_TIMESTAMP)
            ON CONFLICT (user_id)
            DO UPDATE SET
                access_token = EXCLUDED.access_token,
                refresh_token = EXCLUDED.refresh_token,
                token_type = EXCLUDED.token_type,
                scopes = EXCLUDED.scopes,
                expires_at = EXCLUDED.expires_at,
                updated_at = CURRENT_TIMESTAMP
        """, user_id, access_token, refresh_token, token_type, scopes, expires_at)

        logger.info(f"Stored credentials for user: {user_id}")
        return True

    except Exception as e:
        logger.error(f"Error storing user credentials: {str(e)}")
        return False
    finally:
        await conn.close()

async def load_user_credentials(user_id: str) -> Optional[Dict[str, Any]]:
    """Load user credentials from the database"""
    conn = await asyncpg.connect(DATABASE_URL)

    try:
        row = await conn.fetchrow("""
            SELECT user_id, access_token, refresh_token, token_type, scopes, expires_at, created_at, updated_at
            FROM user_credentials
            WHERE user_id = $1
        """, user_id)

        if row:
            credentials = {
                "user_id": row["user_id"],
                "access_token": row["access_token"],
                "refresh_token": row["refresh_token"],
                "token_type": row["token_type"],
                "scopes": list(row["scopes"]) if row["scopes"] else [],
                "expires_at": row["expires_at"],
                "created_at": row["created_at"],
                "updated_at": row["updated_at"]
            }
            logger.info(f"Loaded credentials for user: {user_id}")
            return credentials
        else:
            logger.info(f"No credentials found for user: {user_id}")
            return None

    except Exception as e:
        logger.error(f"Error loading user credentials: {str(e)}")
        return None
    finally:
        await conn.close()

async def load_all_user_credentials() -> Dict[str, Dict[str, Any]]:
    """Load all user credentials from the database"""
    conn = await asyncpg.connect(DATABASE_URL)

    try:
        rows = await conn.fetch("""
            SELECT user_id, access_token, refresh_token, token_type, scopes, expires_at, created_at, updated_at
            FROM user_credentials
        """)

        credentials_dict = {}
        for row in rows:
            user_id = row["user_id"]
            credentials_dict[user_id] = {
                "access_token": row["access_token"],
                "refresh_token": row["refresh_token"],
                "token_type": row["token_type"],
                "scopes": list(row["scopes"]) if row["scopes"] else [],
                "expires_at": row["expires_at"],
                "created_at": row["created_at"],
                "updated_at": row["updated_at"]
            }

        logger.info(f"Loaded credentials for {len(credentials_dict)} users from database")
        return credentials_dict

    except Exception as e:
        logger.error(f"Error loading all user credentials: {str(e)}")
        return {}
    finally:
        await conn.close()

async def delete_user_credentials(user_id: str) -> bool:
    """Delete user credentials from the database"""
    conn = await asyncpg.connect(DATABASE_URL)

    try:
        result = await conn.execute("""
            DELETE FROM user_credentials
            WHERE user_id = $1
        """, user_id)

        # Parse the DELETE count from result string like "DELETE 1"
        count = int(result.split()[1]) if result else 0

        if count > 0:
            logger.info(f"Deleted credentials for user: {user_id}")
            return True
        else:
            logger.info(f"No credentials found to delete for user: {user_id}")
            return False

    except Exception as e:
        logger.error(f"Error deleting user credentials: {str(e)}")
        return False
    finally:
        await conn.close()