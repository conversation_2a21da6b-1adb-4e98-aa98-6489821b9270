"""
Advanced Text Chunking Utilities for GAIA RAG System

This module provides sophisticated text chunking strategies that preserve
semantic meaning and context while optimizing for retrieval performance.
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple
import tiktoken
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ChunkMetadata:
    """Metadata for a text chunk"""
    chunk_index: int
    start_char: int
    end_char: int
    token_count: int
    sentence_count: int
    paragraph_index: Optional[int] = None
    section_title: Optional[str] = None
    chunk_type: str = "content"  # content, header, list, table, etc.

class AdvancedTextChunker:
    """
    Advanced text chunking with semantic awareness and context preservation
    """
    
    def __init__(self, 
                 chunk_size: int = 500, 
                 chunk_overlap: int = 50,
                 encoding_name: str = "cl100k_base"):
        """
        Initialize the chunker
        
        Args:
            chunk_size: Target chunk size in tokens
            chunk_overlap: Overlap between chunks in tokens
            encoding_name: Tokenizer encoding to use
        """
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.encoding = tiktoken.get_encoding(encoding_name)
        
        # Patterns for different text structures
        self.section_pattern = re.compile(r'^#{1,6}\s+(.+)$', re.MULTILINE)
        self.paragraph_pattern = re.compile(r'\n\s*\n')
        self.sentence_pattern = re.compile(r'(?<=[.!?])\s+(?=[A-Z])')
        self.list_pattern = re.compile(r'^[\s]*[-*•]\s+', re.MULTILINE)
        self.numbered_list_pattern = re.compile(r'^[\s]*\d+\.\s+', re.MULTILINE)
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in text"""
        return len(self.encoding.encode(text))
    
    def detect_structure(self, text: str) -> Dict[str, Any]:
        """
        Detect document structure (headers, lists, paragraphs)
        """
        structure = {
            "sections": [],
            "paragraphs": [],
            "lists": [],
            "has_headers": False,
            "has_lists": False
        }
        
        # Find sections/headers
        sections = []
        for match in self.section_pattern.finditer(text):
            sections.append({
                "title": match.group(1).strip(),
                "start": match.start(),
                "end": match.end(),
                "level": len(match.group(0).split()[0])  # Count # symbols
            })
        
        if sections:
            structure["sections"] = sections
            structure["has_headers"] = True
        
        # Find paragraphs
        paragraphs = []
        paragraph_splits = self.paragraph_pattern.split(text)
        current_pos = 0
        for i, para in enumerate(paragraph_splits):
            if para.strip():
                paragraphs.append({
                    "index": i,
                    "start": current_pos,
                    "end": current_pos + len(para),
                    "text": para.strip()
                })
            current_pos += len(para) + 2  # Account for newlines
        
        structure["paragraphs"] = paragraphs
        
        # Detect lists
        if self.list_pattern.search(text) or self.numbered_list_pattern.search(text):
            structure["has_lists"] = True
        
        return structure
    
    def semantic_sentence_chunking(self, text: str) -> List[Dict[str, Any]]:
        """
        Chunk text by sentences while respecting semantic boundaries
        """
        sentences = self.sentence_pattern.split(text)
        chunks = []
        current_chunk = ""
        current_tokens = 0
        chunk_index = 0
        start_char = 0
        
        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue
            
            sentence_tokens = self.count_tokens(sentence)
            
            # Check if adding this sentence would exceed chunk size
            if current_tokens + sentence_tokens > self.chunk_size and current_chunk:
                # Save current chunk
                chunk_metadata = ChunkMetadata(
                    chunk_index=chunk_index,
                    start_char=start_char,
                    end_char=start_char + len(current_chunk),
                    token_count=current_tokens,
                    sentence_count=len(self.sentence_pattern.split(current_chunk)),
                    chunk_type="content"
                )
                
                chunks.append({
                    "text": current_chunk.strip(),
                    "metadata": chunk_metadata
                })
                
                # Start new chunk with overlap
                overlap_text = ""
                if self.chunk_overlap > 0:
                    # Take last few sentences for overlap
                    chunk_sentences = self.sentence_pattern.split(current_chunk)
                    overlap_sentences = chunk_sentences[-2:] if len(chunk_sentences) > 1 else chunk_sentences
                    overlap_text = " ".join(overlap_sentences).strip()
                    
                    if self.count_tokens(overlap_text) > self.chunk_overlap:
                        # Truncate overlap if too long
                        overlap_tokens = self.encoding.encode(overlap_text)
                        overlap_text = self.encoding.decode(overlap_tokens[:self.chunk_overlap])
                
                current_chunk = overlap_text + " " + sentence if overlap_text else sentence
                current_tokens = self.count_tokens(current_chunk)
                chunk_index += 1
                start_char = start_char + len(chunks[-1]["text"]) - len(overlap_text) if overlap_text else start_char + len(chunks[-1]["text"])
            else:
                current_chunk += " " + sentence if current_chunk else sentence
                current_tokens = self.count_tokens(current_chunk)
        
        # Add final chunk
        if current_chunk.strip():
            chunk_metadata = ChunkMetadata(
                chunk_index=chunk_index,
                start_char=start_char,
                end_char=start_char + len(current_chunk),
                token_count=current_tokens,
                sentence_count=len(self.sentence_pattern.split(current_chunk)),
                chunk_type="content"
            )
            
            chunks.append({
                "text": current_chunk.strip(),
                "metadata": chunk_metadata
            })
        
        return chunks
    
    def structure_aware_chunking(self, text: str) -> List[Dict[str, Any]]:
        """
        Chunk text while preserving document structure (headers, paragraphs, lists)
        """
        structure = self.detect_structure(text)
        chunks = []
        
        if structure["has_headers"] and structure["sections"]:
            # Process each section separately
            sections = structure["sections"]
            
            for i, section in enumerate(sections):
                # Determine section boundaries
                section_start = section["start"]
                section_end = sections[i + 1]["start"] if i + 1 < len(sections) else len(text)
                
                section_text = text[section_start:section_end]
                section_title = section["title"]
                
                # Chunk the section content
                section_chunks = self.semantic_sentence_chunking(section_text)
                
                # Add section context to chunks
                for chunk in section_chunks:
                    chunk["metadata"].section_title = section_title
                    chunk["metadata"].chunk_type = "section_content"
                    
                    # Prepend section title for context
                    chunk["text"] = f"[Section: {section_title}]\n{chunk['text']}"
                
                chunks.extend(section_chunks)
        else:
            # No clear structure, use semantic chunking
            chunks = self.semantic_sentence_chunking(text)
        
        return chunks
    
    def adaptive_chunking(self, text: str, document_type: str = "general") -> List[Dict[str, Any]]:
        """
        Adaptive chunking based on document type and content characteristics
        """
        # Adjust parameters based on document type
        if document_type == "technical":
            # Technical documents benefit from larger chunks to preserve context
            self.chunk_size = min(800, self.chunk_size * 1.5)
            self.chunk_overlap = min(100, self.chunk_overlap * 2)
        elif document_type == "legal":
            # Legal documents need precise boundaries
            self.chunk_size = min(600, self.chunk_size * 1.2)
            self.chunk_overlap = min(80, self.chunk_overlap * 1.5)
        elif document_type == "conversational":
            # Conversational content can use smaller chunks
            self.chunk_size = max(300, self.chunk_size * 0.8)
            self.chunk_overlap = max(30, self.chunk_overlap * 0.8)
        
        # Analyze text characteristics
        total_tokens = self.count_tokens(text)
        structure = self.detect_structure(text)
        
        # Choose chunking strategy
        if total_tokens < self.chunk_size:
            # Small document, return as single chunk
            chunk_metadata = ChunkMetadata(
                chunk_index=0,
                start_char=0,
                end_char=len(text),
                token_count=total_tokens,
                sentence_count=len(self.sentence_pattern.split(text)),
                chunk_type="complete_document"
            )
            
            return [{
                "text": text,
                "metadata": chunk_metadata
            }]
        elif structure["has_headers"]:
            # Structured document, use structure-aware chunking
            return self.structure_aware_chunking(text)
        else:
            # Unstructured document, use semantic chunking
            return self.semantic_sentence_chunking(text)
    
    def chunk_with_context_preservation(self, 
                                      text: str, 
                                      preserve_context: bool = True,
                                      document_metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """
        Main chunking method with context preservation
        
        Args:
            text: Text to chunk
            preserve_context: Whether to preserve document context in chunks
            document_metadata: Additional metadata to include in chunks
        
        Returns:
            List of chunks with enhanced metadata
        """
        # Detect document type from metadata or content
        document_type = "general"
        if document_metadata:
            mime_type = document_metadata.get("mime_type", "")
            if "pdf" in mime_type:
                document_type = "technical"
            elif "spreadsheet" in mime_type:
                document_type = "tabular"
            elif "presentation" in mime_type:
                document_type = "presentation"
        
        # Perform adaptive chunking
        chunks = self.adaptive_chunking(text, document_type)
        
        # Enhance chunks with document metadata
        enhanced_chunks = []
        for chunk in chunks:
            chunk_data = {
                "text_chunk": chunk["text"],
                "metadata": {
                    "chunk_index": chunk["metadata"].chunk_index,
                    "token_count": chunk["metadata"].token_count,
                    "sentence_count": chunk["metadata"].sentence_count,
                    "chunk_type": chunk["metadata"].chunk_type,
                    "start_char": chunk["metadata"].start_char,
                    "end_char": chunk["metadata"].end_char
                }
            }
            
            # Add section context if available
            if chunk["metadata"].section_title:
                chunk_data["metadata"]["section_title"] = chunk["metadata"].section_title
            
            # Add document metadata
            if document_metadata:
                chunk_data["metadata"].update(document_metadata)
            
            # Add chunking strategy info
            chunk_data["metadata"]["chunking_strategy"] = "adaptive"
            chunk_data["metadata"]["chunk_size_target"] = self.chunk_size
            chunk_data["metadata"]["chunk_overlap"] = self.chunk_overlap
            
            enhanced_chunks.append(chunk_data)
        
        logger.info(f"Created {len(enhanced_chunks)} chunks using adaptive strategy")
        return enhanced_chunks

# Convenience function for backward compatibility
def advanced_chunk_text(text: str, 
                       chunk_size: int = 500, 
                       chunk_overlap: int = 50,
                       document_metadata: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
    """
    Advanced text chunking with semantic awareness
    
    Args:
        text: Text to chunk
        chunk_size: Target chunk size in tokens
        chunk_overlap: Overlap between chunks in tokens
        document_metadata: Additional metadata to include
    
    Returns:
        List of chunks with metadata
    """
    chunker = AdvancedTextChunker(chunk_size=chunk_size, chunk_overlap=chunk_overlap)
    return chunker.chunk_with_context_preservation(
        text=text, 
        document_metadata=document_metadata
    )
