from pydantic import BaseModel, Field
from typing import List, Literal, Optional, Dict, Any
from datetime import datetime
from uuid import UUID, uuid4


class MessageBase(BaseModel):
    content: str


class MessageCreate(MessageBase):
    pass


class Message(MessageBase):
    id: str = Field(default_factory=lambda: str(uuid4()))
    sender: Literal["user", "ai"]
    content: str
    timestamp: str = Field(default_factory=lambda: datetime.now().strftime("%H:%M %p"))


class ProjectBase(BaseModel):
    title: str
    description: str


class ProjectCreate(ProjectBase):
    pass


class Project(ProjectBase):
    id: str = Field(default_factory=lambda: str(uuid4()))
    timestamp: str = Field(default_factory=lambda: "Just now")
    messages: List[Message] = []


class ChatRequest(BaseModel):
    message: str
    project_id: str


class ChatResponse(BaseModel):
    id: str = Field(default_factory=lambda: str(uuid4()))
    sender: Literal["ai"] = "ai"
    content: str
    timestamp: str = Field(default_factory=lambda: datetime.now().strftime("%H:%M %p"))


# Google OAuth schemas
class GoogleAuthToken(BaseModel):
    token: str
    refresh_token: Optional[str] = None
    token_uri: str
    client_id: str
    scopes: List[str]
    expires_at: Optional[int] = None


class GoogleUserCredentials(BaseModel):
    user_id: str
    credentials: GoogleAuthToken


# Google Drive file schemas
class DriveFileMetadata(BaseModel):
    file_id: str
    name: str
    mime_type: str
    modified_time: str
    web_view_link: Optional[str] = None
    size: Optional[int] = None
    is_indexed: bool = False


class DriveFilesResponse(BaseModel):
    files: List[DriveFileMetadata]
    next_page_token: Optional[str] = None


# RAG schemas - PostgreSQL Implementation
class TextChunk(BaseModel):
    text_chunk: str
    metadata: Dict[str, Any]


class PgRagIngestRequest(BaseModel):
    project_id: str
    user_id: str
    chunks: List[TextChunk]


class PgRagIngestResponse(BaseModel):
    status: str
    processed_chunks: int
    message: str


class PgRagQueryRequest(BaseModel):
    project_id: str
    user_id: str
    query: str
    top_k: int = 5


class PgRagQueryResult(BaseModel):
    id: int
    text_chunk: str
    metadata: Dict[str, Any]
    similarity: float


class PgRagQueryResponse(BaseModel):
    results: List[PgRagQueryResult]


class PgRagStatsResponse(BaseModel):
    project_id: str
    total_chunks: int
    unique_files: int
    is_indexed: bool


# Legacy RAG schemas for Pinecone (for compatibility)
class RagIngestRequest(BaseModel):
    project_id: str
    file_ids: List[str]
    user_id: Optional[str] = None


class RagIngestResponse(BaseModel):
    processed_files: int
    total_chunks: int
    message: str


class RagQueryRequest(BaseModel):
    project_id: str
    query: str
    top_k: int = 3
    include_content: bool = True
    user_id: Optional[str] = None


class RagQueryResult(BaseModel):
    file_id: str
    file_name: str
    chunk_content: str
    score: float
    modified_time: str
    web_view_link: Optional[str] = None


class RagQueryResponse(BaseModel):
    results: List[RagQueryResult] 