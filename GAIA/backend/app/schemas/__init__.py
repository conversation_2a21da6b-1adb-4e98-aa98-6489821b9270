# Automatically generated __init__.py file

# Re-export all schemas to maintain backward compatibility
# Import directly from the original schemas.py file path
import sys
import os
from importlib.util import spec_from_file_location, module_from_spec

# Get the path to the original schemas.py file
current_dir = os.path.dirname(os.path.abspath(__file__))
app_dir = os.path.dirname(current_dir)
schemas_path = os.path.join(app_dir, 'schemas.py')

# Load the original schemas.py module
spec = spec_from_file_location('original_schemas', schemas_path)
original_schemas = module_from_spec(spec)
spec.loader.exec_module(original_schemas)

# Re-export all schemas from the original module
MessageBase = original_schemas.MessageBase
MessageCreate = original_schemas.MessageCreate
Message = original_schemas.Message
ProjectBase = original_schemas.ProjectBase
ProjectCreate = original_schemas.ProjectCreate
Project = original_schemas.Project
ChatRequest = original_schemas.ChatRequest
ChatResponse = original_schemas.ChatResponse
GoogleAuthToken = original_schemas.GoogleAuthToken
GoogleUserCredentials = original_schemas.GoogleUserCredentials
OriginalDriveFileMetadata = original_schemas.DriveFileMetadata
DriveFilesResponse = original_schemas.DriveFilesResponse
TextChunk = original_schemas.TextChunk
PgRagIngestRequest = original_schemas.PgRagIngestRequest
PgRagIngestResponse = original_schemas.PgRagIngestResponse
PgRagQueryRequest = original_schemas.PgRagQueryRequest
PgRagQueryResponse = original_schemas.PgRagQueryResponse
PgRagQueryResult = original_schemas.PgRagQueryResult
PgRagStatsResponse = original_schemas.PgRagStatsResponse
RagIngestRequest = original_schemas.RagIngestRequest
RagIngestResponse = original_schemas.RagIngestResponse
RagQueryRequest = original_schemas.RagQueryRequest
RagQueryResult = original_schemas.RagQueryResult
RagQueryResponse = original_schemas.RagQueryResponse

# Export new schemas
try:
    from .drive import DriveSyncRequest, DriveSyncResponse, DriveFileMetadata
    from .rag import (
        RagChunk, RagIngestRequest as NewRagIngestRequest, 
        RagIngestResponse as NewRagIngestResponse,
        RagQueryRequest as NewRagQueryRequest, 
        RagQueryResponse as NewRagQueryResponse,
        RagQueryResult as NewRagQueryResult,
        RagProjectStats
    )
except ImportError:
    # If the new schema files are not available, provide default values
    print("Warning: New schema files not found, using defaults")
