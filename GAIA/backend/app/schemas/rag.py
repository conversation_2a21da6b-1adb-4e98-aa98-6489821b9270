from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class Rag<PERSON>hunk(BaseModel):
    """Model for a text chunk with metadata"""
    text_chunk: str
    metadata: Dict[str, Any]


class RagIngestRequest(BaseModel):
    """Request model for RAG ingest endpoint"""
    project_id: str
    user_id: str
    chunks: List[RagChunk]


class RagIngestResponse(BaseModel):
    """Response model for RAG ingest endpoint"""
    status: str
    processed_chunks: int
    message: str
    last_ingested: str = Field(default_factory=lambda: datetime.now().isoformat())
    error: Optional[Dict[str, Any]] = None


class RagQueryRequest(BaseModel):
    """Request model for RAG query endpoint"""
    project_id: str
    query: str
    top_k: int = 5
    similarity_threshold: float = 0.7


class RagQueryResult(BaseModel):
    """Model for a single RAG query result"""
    id: int
    text_chunk: str
    metadata: Dict[str, Any]
    similarity: float


class RagQueryResponse(BaseModel):
    """Response model for RAG query endpoint"""
    results: List[RagQueryResult]
    error: Optional[Dict[str, Any]] = None


class RagProjectStats(BaseModel):
    """Model for RAG project statistics"""
    project_id: str
    total_chunks: int
    unique_files: int
    is_indexed: bool
    last_synced: Optional[str] = None 