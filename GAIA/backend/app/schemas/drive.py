from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

class DriveSyncRequest(BaseModel):
    """Request model for Drive sync endpoint"""
    project_id: str
    user_id: str
    max_files: int = 100
    file_types: Optional[List[str]] = None


class DriveSyncResponse(BaseModel):
    """Response model for Drive sync endpoint"""
    status: str
    files_indexed: int
    total_chunks: int
    last_synced: str = Field(default_factory=lambda: datetime.now().isoformat())
    error: Optional[Dict[str, Any]] = None


class DriveFileMetadata(BaseModel):
    """Model for Google Drive file metadata"""
    file_id: str
    name: str
    mime_type: str
    modified_time: str
    web_view_link: Optional[str] = None
    size: Optional[str] = None
    is_indexed: bool = False


class DriveFileListResponse(BaseModel):
    """Response model for Drive file list endpoint"""
    files: List[DriveFileMetadata]
    next_page_token: Optional[str] = None 