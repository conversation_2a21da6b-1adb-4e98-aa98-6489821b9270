# PostgreSQL + pgvector RAG Migration Guide

This document outlines the migration of GAIA's RAG (Retrieval-Augmented Generation) system from Pinecone to PostgreSQL with the pgvector extension.

## Overview

We've implemented a new PostgreSQL-based vector storage system for GAIA's RAG functionality, replacing the previous Pinecone implementation. This migration provides:

- Self-hosted vector database using PostgreSQL
- Efficient vector similarity search with pgvector
- Better integration with existing database infrastructure
- Cost-effective scaling without external service dependencies

## Technology Stack

- **PostgreSQL** - The database engine
- **pgvector** - PostgreSQL extension for vector operations
- **asyncpg** - Asynchronous PostgreSQL client for Python
- **OpenAI** - For generating text embeddings

## PostgreSQL Setup

### 1. Install pgvector Extension

The pgvector extension needs to be installed in your PostgreSQL instance:

```sql
CREATE EXTENSION IF NOT EXISTS vector;
```

### 2. Create Table Schema

We've implemented a table for storing text chunks and their vector embeddings:

```sql
CREATE TABLE embeddings (
  id SERIAL PRIMARY KEY,
  project_id VARCHAR(255),
  user_id VARCHAR(255),
  text_chunk TEXT,
  embedding vector(1536),
  metadata JSONB
);
```

### 3. Create Vector Index

For efficient similarity search, we use an IVFFlat index:

```sql
CREATE INDEX ON embeddings USING ivfflat (embedding vector_cosine_ops)
WITH (lists = 100);
```

## API Endpoints

We've implemented new endpoints while maintaining backward compatibility:

### New PostgreSQL RAG endpoints

- `POST /api/pgrag/ingest` - Store chunks and their embeddings
- `POST /api/pgrag/query` - Query for semantically similar chunks
- `DELETE /api/pgrag/project/{project_id}` - Delete all embeddings for a project
- `GET /api/pgrag/status/{project_id}` - Get statistics about indexed content

### Legacy Pinecone endpoints (still available)

- `POST /api/rag/ingest`
- `POST /api/rag/query`
- `DELETE /api/rag/project/{project_id}`
- `GET /api/rag/status/{project_id}`

## Migration Process

To migrate existing data from Pinecone to PostgreSQL:

1. Use the bulk ingestion script:

```bash
python backend/scripts/bulk_ingest.py --project_id <project_id> --user_id <user_id>
```

2. Update your frontend to use the new PostgreSQL endpoints or set `use_postgres_rag=true` in chat requests.

## PostgreSQL Configuration

The database connection string is configured in the `.env` file:

```
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/gaia
```

Adjust this to match your PostgreSQL setup.

## Vector Similarity Search

Query for similar vectors using PostgreSQL's vector operators:

```sql
SELECT text_chunk, metadata, 1 - (embedding <=> query_embedding) AS similarity
FROM embeddings
WHERE project_id = 'my_project'
ORDER BY embedding <=> query_embedding
LIMIT 5;
```

Where `<=>` is the cosine distance operator, and we convert it to similarity with `1 - distance`.

## Testing the Migration

1. Enable the new PostgreSQL RAG by setting `use_postgres_rag=true` in chat requests
2. Verify that responses include relevant context from your documents
3. Monitor database performance during operations 