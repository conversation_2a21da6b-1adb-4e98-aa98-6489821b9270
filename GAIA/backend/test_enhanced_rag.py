#!/usr/bin/env python3
"""
Test Script for Enhanced RAG System

This script tests the core functionality of the enhanced RAG system:
- Database connectivity
- Embedding generation
- Vector storage and retrieval
- Claude integration
- Advanced chunking
"""

import asyncio
import logging
import os
import json
from typing import Dict, Any, List
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

async def test_database_connection():
    """Test PostgreSQL connection and pgvector functionality"""
    logger.info("Testing database connection...")
    
    try:
        from app.utils.db_utils import get_db_connection, init_db
        
        # Initialize database
        await init_db()
        logger.info("✅ Database initialization successful")
        
        # Test connection
        conn = await get_db_connection()
        
        # Test vector operations
        test_vector = [0.1, 0.2, 0.3] + [0.0] * 1533  # 1536 dimensions
        await conn.execute(
            "SELECT $1::vector <=> $2::vector AS similarity",
            test_vector, test_vector
        )
        
        await conn.close()
        logger.info("✅ Vector operations test successful")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database test failed: {str(e)}")
        return False

async def test_embedding_generation():
    """Test OpenAI embedding generation"""
    logger.info("Testing embedding generation...")
    
    try:
        from app.utils.db_utils import get_embedding
        
        test_text = "This is a test document for embedding generation."
        embedding = await get_embedding(test_text)
        
        if len(embedding) == 1536:
            logger.info("✅ Embedding generation successful")
            return True
        else:
            logger.error(f"❌ Unexpected embedding dimension: {len(embedding)}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Embedding generation failed: {str(e)}")
        return False

async def test_advanced_chunking():
    """Test advanced text chunking functionality"""
    logger.info("Testing advanced chunking...")
    
    try:
        from app.utils.advanced_chunking import AdvancedTextChunker
        
        test_text = """
        # Introduction
        This is a test document with multiple sections.
        
        ## Section 1
        This section contains important information about the first topic.
        It has multiple sentences to test the chunking algorithm.
        
        ## Section 2
        This is another section with different content.
        - Item 1
        - Item 2
        - Item 3
        
        The chunking should preserve the structure and context.
        """
        
        chunker = AdvancedTextChunker(chunk_size=200, chunk_overlap=20)
        chunks = chunker.chunk_with_context_preservation(
            text=test_text,
            document_metadata={"file_name": "test.md", "mime_type": "text/markdown"}
        )
        
        if len(chunks) > 0:
            logger.info(f"✅ Advanced chunking successful: {len(chunks)} chunks created")
            for i, chunk in enumerate(chunks[:2]):  # Show first 2 chunks
                logger.info(f"  Chunk {i+1}: {len(chunk['text_chunk'])} chars, {chunk['metadata']['token_count']} tokens")
            return True
        else:
            logger.error("❌ No chunks generated")
            return False
            
    except Exception as e:
        logger.error(f"❌ Advanced chunking failed: {str(e)}")
        return False

async def test_rag_storage_and_retrieval():
    """Test RAG storage and retrieval functionality"""
    logger.info("Testing RAG storage and retrieval...")
    
    try:
        from app.utils.db_utils import store_embeddings, query_embeddings
        from app.utils.advanced_chunking import AdvancedTextChunker
        
        # Test project
        project_id = "test-project"
        user_id = "test-user"
        
        # Create test documents
        test_documents = [
            {
                "text": "Artificial intelligence is transforming the way we work and live. Machine learning algorithms can process vast amounts of data.",
                "metadata": {"file_name": "ai_overview.txt", "source": "test"}
            },
            {
                "text": "Climate change is one of the most pressing issues of our time. Renewable energy sources like solar and wind power are crucial.",
                "metadata": {"file_name": "climate_report.txt", "source": "test"}
            },
            {
                "text": "The stock market showed significant volatility this quarter. Technology stocks performed particularly well.",
                "metadata": {"file_name": "market_analysis.txt", "source": "test"}
            }
        ]
        
        # Chunk and store documents
        chunker = AdvancedTextChunker(chunk_size=100, chunk_overlap=10)
        all_chunks = []
        
        for doc in test_documents:
            chunks = chunker.chunk_with_context_preservation(
                text=doc["text"],
                document_metadata=doc["metadata"]
            )
            all_chunks.extend(chunks)
        
        # Store embeddings
        stored_count = await store_embeddings(
            project_id=project_id,
            user_id=user_id,
            chunks=all_chunks
        )
        
        logger.info(f"✅ Stored {stored_count} chunks")
        
        # Test retrieval
        test_queries = [
            "What is artificial intelligence?",
            "Tell me about climate change",
            "How did the stock market perform?"
        ]
        
        for query in test_queries:
            results = await query_embeddings(
                project_id=project_id,
                query=query,
                limit=2
            )
            
            if results:
                logger.info(f"✅ Query '{query}' returned {len(results)} results")
                best_result = results[0]
                logger.info(f"  Best match: {best_result['similarity']:.3f} similarity")
            else:
                logger.warning(f"⚠️ Query '{query}' returned no results")
        
        # Cleanup test data
        from app.utils.db_utils import get_db_connection
        conn = await get_db_connection()
        await conn.execute("DELETE FROM embeddings WHERE project_id = $1", project_id)
        await conn.close()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ RAG storage and retrieval test failed: {str(e)}")
        return False

async def test_claude_integration():
    """Test Claude 4 Sonnet integration"""
    logger.info("Testing Claude integration...")
    
    try:
        from app.services.enhanced_rag_service import EnhancedRagService
        
        # Check if Anthropic API key is available
        if not os.getenv('ANTHROPIC_API_KEY'):
            logger.warning("⚠️ ANTHROPIC_API_KEY not set, skipping Claude test")
            return True
        
        rag_service = EnhancedRagService()
        
        # Test query analysis
        test_query = "What are the benefits of renewable energy?"
        analysis = await rag_service.intelligent_query_processing(test_query)
        
        if analysis and 'intent' in analysis:
            logger.info("✅ Claude query analysis successful")
            logger.info(f"  Intent: {analysis.get('intent')}")
            logger.info(f"  Complexity: {analysis.get('complexity')}")
            return True
        else:
            logger.error("❌ Claude query analysis failed")
            return False
            
    except Exception as e:
        logger.error(f"❌ Claude integration test failed: {str(e)}")
        return False

async def test_background_tasks():
    """Test background task system"""
    logger.info("Testing background task system...")
    
    try:
        from app.services.background_tasks import TaskManager, get_all_active_tasks
        
        # Test task manager
        active_projects = TaskManager.get_active_projects()
        logger.info(f"✅ Task manager working: {len(active_projects)} active projects")
        
        # Test task monitoring
        active_tasks = get_all_active_tasks()
        logger.info(f"✅ Task monitoring working: {len(active_tasks)} active tasks")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Background task test failed: {str(e)}")
        return False

async def run_all_tests():
    """Run all tests and provide summary"""
    logger.info("🚀 Starting Enhanced RAG System Tests")
    logger.info("=" * 50)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Embedding Generation", test_embedding_generation),
        ("Advanced Chunking", test_advanced_chunking),
        ("RAG Storage & Retrieval", test_rag_storage_and_retrieval),
        ("Claude Integration", test_claude_integration),
        ("Background Tasks", test_background_tasks),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running: {test_name}")
        logger.info("-" * 30)
        
        try:
            success = await test_func()
            results[test_name] = success
        except Exception as e:
            logger.error(f"❌ {test_name} failed with exception: {str(e)}")
            results[test_name] = False
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("📊 Test Summary")
    logger.info("=" * 50)
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
    
    logger.info(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Enhanced RAG system is ready.")
    else:
        logger.warning(f"⚠️ {total - passed} tests failed. Please check the issues above.")
    
    return passed == total

def main():
    """Main test function"""
    try:
        success = asyncio.run(run_all_tests())
        exit_code = 0 if success else 1
    except KeyboardInterrupt:
        logger.info("\n⏹️ Tests interrupted by user")
        exit_code = 1
    except Exception as e:
        logger.error(f"💥 Test runner failed: {str(e)}")
        exit_code = 1
    
    exit(exit_code)

if __name__ == "__main__":
    main()
