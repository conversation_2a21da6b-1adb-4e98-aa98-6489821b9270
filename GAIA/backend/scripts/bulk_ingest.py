#!/usr/bin/env python
"""
Bulk Ingestion Script for Google Drive Documents to PostgreSQL RAG

This script:
1. Fetches documents from Google Drive based on project_id
2. Extracts text, chunks it, and embeds it
3. Stores the embeddings in PostgreSQL using the pgrag API

Usage:
python bulk_ingest.py --project_id <project_id> --user_id <user_id> [--mime_types pdf,docx,txt]

Requirements:
- Valid Google Drive authentication (user_id must be authenticated)
- The backend API must be running
"""

import argparse
import asyncio
import aiohttp
import os
import sys
import json
import io
import logging
from typing import List, Dict, Any, Optional
import docx
from PyPDF2 import PdfReader
from dotenv import load_dotenv
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseDownload
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger("bulk_ingest")

# Load environment variables
load_dotenv()

# Backend API URL
API_URL = os.getenv("API_URL", "http://localhost:8000/api")


async def fetch_user_credentials(user_id: str) -> Dict[str, Any]:
    """Fetch user credentials from the API"""
    async with aiohttp.ClientSession() as session:
        url = f"{API_URL}/auth/google/credentials/{user_id}"
        async with session.get(url) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(f"Failed to get user credentials: {error_text}")
            return await response.json()


async def fetch_drive_files(user_id: str, project_id: str, mime_types: Optional[str] = None) -> List[Dict[str, Any]]:
    """Fetch files from Google Drive via the API"""
    async with aiohttp.ClientSession() as session:
        url = f"{API_URL}/drive/files"
        params = {
            "user_id": user_id,
            "project_id": project_id
        }
        
        if mime_types:
            params["mime_types"] = mime_types
            
        async with session.get(url, params=params) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(f"Failed to fetch files: {error_text}")
            
            data = await response.json()
            return data.get("files", [])


async def download_file_content(user_id: str, project_id: str, file_id: str):
    """Download a file's content via the API"""
    # This would be a simplified implementation. In production,
    # you might want to download directly from Google Drive.
    pass


def extract_text_from_file(file_content, mime_type):
    """Extract text from various file types"""
    text = ""
    
    if "pdf" in mime_type:
        # Handle PDF files
        pdf_reader = PdfReader(io.BytesIO(file_content))
        for page in pdf_reader.pages:
            text += page.extract_text() + "\n"
            
    elif "document" in mime_type:
        # Handle DOCX files
        doc = docx.Document(io.BytesIO(file_content))
        for para in doc.paragraphs:
            text += para.text + "\n"
            
    elif "text/plain" in mime_type:
        # Handle text files
        text = file_content.decode("utf-8")
        
    return text


def chunk_text(text: str, max_tokens: int = 400):
    """Split text into chunks of roughly max_tokens size"""
    sentences = text.replace('\n', ' ').split('. ')
    chunks = []
    current_chunk = ""
    
    for sentence in sentences:
        # Rough approximation of token count (4 chars = ~1 token)
        sentence_tokens = len(sentence) / 4
        current_chunk_tokens = len(current_chunk) / 4
        
        if current_chunk_tokens + sentence_tokens > max_tokens and current_chunk:
            chunks.append(current_chunk.strip())
            current_chunk = sentence + ". "
        else:
            current_chunk += sentence + ". "
    
    if current_chunk:
        chunks.append(current_chunk.strip())
        
    return chunks


async def ingest_chunks(project_id: str, user_id: str, chunks: List[Dict[str, Any]]):
    """Ingest chunks into PostgreSQL via the API"""
    async with aiohttp.ClientSession() as session:
        url = f"{API_URL}/pgrag/ingest"
        
        payload = {
            "project_id": project_id,
            "user_id": user_id,
            "chunks": chunks
        }
        
        async with session.post(url, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(f"Failed to ingest chunks: {error_text}")
            
            return await response.json()


async def process_file(drive_service, project_id: str, user_id: str, file):
    """Process a single file from Google Drive"""
    try:
        file_id = file["file_id"]
        mime_type = file["mime_type"]
        
        # Check if file type is supported
        if not ("pdf" in mime_type or "document" in mime_type or "text/plain" in mime_type):
            logger.warning(f"Skipping unsupported file type: {mime_type} for {file['name']}")
            return 0
        
        logger.info(f"Processing file: {file['name']} ({file_id})")
        
        # Download file from Google Drive
        request = drive_service.files().get_media(fileId=file_id)
        file_content = io.BytesIO()
        downloader = MediaIoBaseDownload(file_content, request)
        
        done = False
        while not done:
            status, done = downloader.next_chunk()
            logger.info(f"Download {int(status.progress() * 100)}%")
        
        file_content = file_content.getvalue()
        
        # Extract text
        text = extract_text_from_file(file_content, mime_type)
        
        if not text:
            logger.warning(f"No text extracted from file: {file['name']}")
            return 0
        
        # Chunk text
        chunks = chunk_text(text)
        
        if not chunks:
            logger.warning(f"No chunks generated from file: {file['name']}")
            return 0
        
        logger.info(f"Generated {len(chunks)} chunks from {file['name']}")
        
        # Prepare chunks with metadata
        chunk_objects = []
        for i, chunk_text in enumerate(chunks):
            chunk_objects.append({
                "text_chunk": chunk_text,
                "metadata": {
                    "file_id": file_id,
                    "file_name": file["name"],
                    "mime_type": mime_type,
                    "chunk_index": i,
                    "modified_time": file["modified_time"],
                    "web_view_link": file.get("web_view_link", "")
                }
            })
        
        # Ingest in batches of 10 to avoid overwhelming API
        batch_size = 10
        total_ingested = 0
        
        for i in range(0, len(chunk_objects), batch_size):
            batch = chunk_objects[i:i+batch_size]
            result = await ingest_chunks(project_id, user_id, batch)
            total_ingested += result.get("processed_chunks", 0)
            logger.info(f"Ingested batch {i//batch_size + 1}/{(len(chunk_objects)-1)//batch_size + 1}: {result.get('processed_chunks')} chunks")
            
            # Small delay to prevent rate limiting
            await asyncio.sleep(1)
        
        logger.info(f"Successfully processed {file['name']}: {total_ingested} chunks")
        return total_ingested
    
    except Exception as e:
        logger.error(f"Error processing file {file.get('name', 'unknown')}: {str(e)}")
        return 0


async def main():
    parser = argparse.ArgumentParser(description="Bulk ingest documents from Google Drive to PostgreSQL RAG")
    parser.add_argument("--project_id", required=True, help="Project ID to ingest documents for")
    parser.add_argument("--user_id", required=True, help="User ID with Google Drive access")
    parser.add_argument("--mime_types", default="pdf,document,text/plain", help="Comma-separated list of MIME types to process")
    
    args = parser.parse_args()
    
    try:
        # Fetch user credentials
        try:
            user_creds = await fetch_user_credentials(args.user_id)
            logger.info(f"Retrieved credentials for user {args.user_id}")
        except Exception as e:
            logger.error(f"Failed to get user credentials: {str(e)}")
            return
        
        # Fetch files from Drive
        try:
            files = await fetch_drive_files(args.user_id, args.project_id, args.mime_types)
            logger.info(f"Found {len(files)} files in Google Drive")
        except Exception as e:
            logger.error(f"Failed to fetch files from Drive: {str(e)}")
            return
        
        if not files:
            logger.warning("No files found to process")
            return
        
        # Get Google Drive service (simplified - normally you'd use tokens from user_creds)
        # In a production version, this would authenticate with Google Drive using user tokens
        drive_service = None  # Placeholder for actual drive service
        
        # Process files
        total_chunks = 0
        for file in files:
            chunks_processed = await process_file(drive_service, args.project_id, args.user_id, file)
            total_chunks += chunks_processed
        
        logger.info(f"Bulk ingestion complete: {total_chunks} total chunks from {len(files)} files")
        
    except Exception as e:
        logger.error(f"Error during bulk ingestion: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(asyncio.run(main())) 