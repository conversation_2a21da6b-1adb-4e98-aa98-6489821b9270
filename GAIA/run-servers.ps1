# PowerShell script to run backend and frontend in sequence

Write-Host "Starting GAIA - GPT AI Assistant..." -ForegroundColor Green
Write-Host "----------------------------------" -ForegroundColor Green

# Define the script root location
$scriptRoot = $PSScriptRoot

# First, start the backend
Write-Host "Starting backend server..." -ForegroundColor Yellow

# Change to the backend directory
Set-Location -Path "$scriptRoot\backend"
Write-Host "Changed to backend directory: $PWD" -ForegroundColor Cyan

# Start the backend server
Start-Process powershell -ArgumentList "-NoExit", "-Command", "python run.py"

# Wait a moment for the backend to initialize
Start-Sleep -Seconds 2

# Return to the original directory
Set-Location -Path $scriptRoot
Write-Host "Changed back to: $PWD" -ForegroundColor Cyan

# Start the frontend
Write-Host "Starting frontend server..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "npm start"

Write-Host "Servers started!" -ForegroundColor Green
Write-Host "Frontend will be available at: http://localhost:3000" -ForegroundColor Cyan
Write-Host "Backend API will be available at: http://localhost:8000" -ForegroundColor Cyan 