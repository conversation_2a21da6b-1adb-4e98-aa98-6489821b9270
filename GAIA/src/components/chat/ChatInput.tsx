import React, { ChangeEvent, KeyboardEvent, useState, useEffect } from 'react';
import { useAppContext } from '../../context/AppContext';

interface ChatInputProps {
  value?: string;
  onChange?: (e: ChangeEvent<HTMLTextAreaElement>) => void;
  onSend?: () => void;
}

const ChatInput: React.FC<ChatInputProps> = ({ value, onChange, onSend }) => {
  const { sendMessage, isLoading } = useAppContext();
  const [inputValue, setInputValue] = useState('');
  const [height, setHeight] = useState<number>(60); // Initial height
  
  // Determine if we're using controlled or uncontrolled input
  const isControlled = value !== undefined && onChange !== undefined;
  
  // Create a dependency value for the effect
  const textToResize = isControlled ? value : inputValue;

  // Resize textarea based on content
  useEffect(() => {
    const textarea = document.querySelector('textarea');
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${Math.min(textarea.scrollHeight, 150)}px`;
      setHeight(Math.min(textarea.scrollHeight, 150));
    }
  }, [textToResize]);

  const handleKeyPress = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };
  
  const handleInputChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    if (isControlled) {
      onChange(e);
    } else {
      setInputValue(e.target.value);
    }
  };
  
  const handleSendMessage = () => {
    const messageText = isControlled ? value : inputValue;
    
    if (messageText && messageText.trim()) {
      if (isControlled && onSend) {
        onSend();
      } else {
        sendMessage(messageText);
        setInputValue('');
      }
    }
  };

  const currentValue = isControlled ? value : inputValue;
  
  return (
    <div className="relative bg-white rounded-lg border border-gray-200 transition-all duration-150">
      <textarea
        className="w-full p-3 pr-12 resize-none rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        placeholder="Ask a question about your data..."
        rows={1}
        style={{ minHeight: `${height}px` }}
        value={currentValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyPress}
        disabled={isLoading}
      />
      <button
        className={`absolute bottom-3 right-3 rounded-full p-2 transition-colors duration-150 ${
          currentValue?.trim() && !isLoading 
            ? 'bg-blue-600 text-white hover:bg-blue-700' 
            : 'bg-gray-200 text-gray-400 cursor-not-allowed'
        }`}
        onClick={handleSendMessage}
        disabled={!currentValue?.trim() || isLoading}
        aria-label="Send message"
      >
        {isLoading ? (
          <svg className="animate-spin h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        ) : (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
          </svg>
        )}
      </button>
    </div>
  );
};

export default ChatInput; 