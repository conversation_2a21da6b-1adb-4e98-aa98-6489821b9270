import React from 'react';
import { Message } from '../../context/AppContext';
import remarkGfm from 'remark-gfm';
import ReactMarkdown from 'react-markdown';

interface ChatMessageProps {
  message: Message;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message }) => {
  // Function to highlight document references in the message
  const highlightDocumentReferences = (content: string) => {
    // Add markdown formatting for document references
    // This makes document citations stand out visually
    return content
      .replace(/\[Document: ([^\]]+)\]/g, '📄 **Document: $1**')
      .replace(/\[Document (\d+)\] ([^:]+):/g, '📄 **Document $1: $2**');
  };

  // Format the message content to enhance readability
  const formattedContent = highlightDocumentReferences(message.content || '');

  return (
    <div 
      className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
    >
      <div 
        className={`min-w-[120px] max-w-[85%] md:max-w-[75%] p-3 rounded-lg ${
          message.sender === 'user' 
            ? 'bg-blue-600 text-white rounded-tr-none' 
            : 'bg-gray-100 text-gray-800 rounded-tl-none'
        }`}
      >
        {message.sender === 'ai' ? (
          <div className="prose prose-sm max-w-none">
            <ReactMarkdown remarkPlugins={[remarkGfm]}>
              {formattedContent || 'Thinking...'}
            </ReactMarkdown>
          </div>
        ) : (
          <p className="whitespace-pre-wrap break-words">{message.content}</p>
        )}
        <div 
          className={`text-xs mt-1 ${
            message.sender === 'user' ? 'text-blue-200' : 'text-gray-500'
          }`}
        >
          {message.timestamp}
        </div>
      </div>
    </div>
  );
};

export default ChatMessage; 