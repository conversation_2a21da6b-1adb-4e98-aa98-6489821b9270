import React, { useState, useEffect } from 'react';
import { useAppContext } from '../context/AppContext';
import ToggleSwitch from './common/ToggleSwitch';

const RightSidebar: React.FC = () => {
  const { dataSources, toggleDataSource, selectedProject, projectDocuments } = useAppContext();
  const [isOpen, setIsOpen] = useState(true);
  const [googleAuth, setGoogleAuth] = useState<{authenticated: boolean, userId: string | null}>({
    authenticated: false,
    userId: null
  });
  const [syncStatus, setSyncStatus] = useState<{
    isSyncing: boolean;
    lastSynced: string | null;
    syncedFiles: number;
    totalChunks: number;
    error: string | null;
  }>({
    isSyncing: false,
    lastSynced: null,
    syncedFiles: 0,
    totalChunks: 0,
    error: null
  });

  // Check Google authentication status on mount and when selected project changes
  useEffect(() => {
    checkGoogleAuthStatus();
    
    // If project changes, check sync status
    if (selectedProject) {
      checkDriveSyncStatus();
      
      // If Drive is enabled, check sync status
      const driveSource = dataSources.find(source => source.id === 'drive');
      if (driveSource && driveSource.enabled) {
        checkDriveSyncStatus();
      }
    }
  }, [selectedProject]);
  
  // Additional effect to monitor data sources changes
  useEffect(() => {
    const driveSource = dataSources.find(source => source.id === 'drive');
    if (selectedProject && driveSource && driveSource.enabled) {
      checkDriveSyncStatus();
    }
  }, [dataSources]);

  const checkGoogleAuthStatus = async () => {
    try {
      // Check if we have stored auth data
      const storedAuth = localStorage.getItem('googleAuth');
      
      if (storedAuth) {
        const authData = JSON.parse(storedAuth);
        const userId = authData.userId;
        
        if (userId) {
          // Try to fetch user credentials
          const response = await fetch(`http://localhost:8000/api/auth/google/credentials/${userId}`);
          
          if (response.ok) {
            const data = await response.json();
            setGoogleAuth({ authenticated: true, userId });
          } else {
            // If 404 or other error, clear the stored auth
            console.log('Google credentials not found or expired, clearing local storage');
            localStorage.removeItem('googleAuth');
            setGoogleAuth({ authenticated: false, userId: null });
          }
        }
      }
    } catch (error) {
      console.error('Error checking Google auth status:', error);
      // Clear local storage on error
      localStorage.removeItem('googleAuth');
      setGoogleAuth({ authenticated: false, userId: null });
    }
  };

  const handleAuthGoogle = () => {
    // Store the current project ID in localStorage to redirect back
    if (selectedProject) {
      localStorage.setItem('authRedirectProject', selectedProject.id);
    }
    
    // Redirect to Google auth endpoint
    const projectParam = selectedProject ? `?project_id=${selectedProject.id}` : '';
    window.location.href = `http://localhost:8000/api/auth/google/login${projectParam}`;
  };

  // Listen for auth redirects
  useEffect(() => {
    // Check URL parameters for auth success
    const urlParams = new URLSearchParams(window.location.search);
    const authSuccess = urlParams.get('auth_success');
    const userId = urlParams.get('user_id');
    
    if (authSuccess === 'true' && userId) {
      // Store authentication data
      localStorage.setItem('googleAuth', JSON.stringify({
        authenticated: true,
        userId: userId
      }));
      
      // Update state
      setGoogleAuth({
        authenticated: true,
        userId: userId
      });
      
      // Check if Drive toggle is enabled or enable it
      const driveSource = dataSources.find(source => source.id === 'drive');
      if (driveSource && !driveSource.enabled) {
        toggleDataSource('drive');
      }
      
      // Remove URL parameters
      window.history.replaceState({}, document.title, window.location.pathname);
      
      // Trigger a sync
      syncWithGoogleDrive();
    }
  }, []);

  const handleDisconnect = async () => {
    try {
      if (googleAuth.userId) {
        // Call the revoke endpoint
        const response = await fetch(`http://localhost:8000/api/auth/google/revoke/${googleAuth.userId}`, {
          method: 'POST'
        });
        
        if (response.ok) {
          // Clear local storage and state
          localStorage.removeItem('googleAuth');
          setGoogleAuth({ authenticated: false, userId: null });
        } else {
          console.error('Failed to revoke access:', await response.text());
        }
      }
    } catch (error) {
      console.error('Error disconnecting from Google:', error);
    }
  };

  // Sync with Google Drive
  const syncWithGoogleDrive = async () => {
    if (!selectedProject || !googleAuth.userId) {
      setSyncStatus(prev => ({
        ...prev,
        error: 'Project or Google authentication not available'
      }));
      return;
    }
    
    try {
      // Set syncing state
      setSyncStatus(prev => ({
        ...prev,
        isSyncing: true,
        error: null
      }));
      
      // Call the Drive sync endpoint
      const response = await fetch(
        `http://localhost:8000/api/drive/sync?project_id=${selectedProject.id}&user_id=${googleAuth.userId}`,
        { 
          method: 'GET',
          headers: {
            'Accept': 'application/json',
          },
        }
      );
      
      if (!response.ok) {
        throw new Error(`Server responded with status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.status === 'syncing_started' && data.job_id) {
        // Start polling for job status
        const jobId = data.job_id;
        pollSyncStatus(jobId);
      } else if (data.status === 'error') {
        // Handle error
        setSyncStatus({
          isSyncing: false,
          lastSynced: null,
          syncedFiles: 0,
          totalChunks: 0,
          error: data.error?.message || 'Sync failed with unknown error'
        });
      }
    } catch (error: any) {
      console.error('Error syncing with Google Drive:', error);
      setSyncStatus({
        isSyncing: false,
        lastSynced: null,
        syncedFiles: 0,
        totalChunks: 0,
        error: `Failed to connect to sync service: ${error.message || 'Unknown error'}`
      });
    }
  };

  // Poll for sync job status
  const pollSyncStatus = async (jobId: string) => {
    try {
      // Set up polling interval
      const pollInterval = setInterval(async () => {
        try {
          // Call status endpoint
          const response = await fetch(`http://localhost:8000/api/drive/sync/status?job_id=${jobId}`);
          
          if (!response.ok) {
            clearInterval(pollInterval);
            throw new Error(`Status check failed with status: ${response.status}`);
          }
          
          const data = await response.json();
          
          // Update UI with current progress
          if (data.status === 'in_progress') {
            // Update progress indicators
            setSyncStatus(prev => ({
              ...prev,
              isSyncing: true,
              syncedFiles: data.files_found || 0,
              totalChunks: data.chunks_generated || 0,
            }));
          } 
          else if (data.status === 'completed') {
            // Sync completed successfully
            clearInterval(pollInterval);
            setSyncStatus({
              isSyncing: false,
              lastSynced: data.last_synced || data.completed_at,
              syncedFiles: data.files_indexed || 0,
              totalChunks: data.total_chunks || 0,
              error: null
            });
            
            // Force refresh project status
            checkDriveSyncStatus();
          } 
          else if (data.status === 'failed') {
            // Sync failed
            clearInterval(pollInterval);
            setSyncStatus({
              isSyncing: false,
              lastSynced: null,
              syncedFiles: 0,
              totalChunks: 0,
              error: data.error || 'Sync process failed'
            });
          }
        } catch (error: any) {
          console.error('Error polling sync status:', error);
          clearInterval(pollInterval);
          setSyncStatus(prev => ({
            ...prev,
            isSyncing: false,
            error: `Error checking sync status: ${error.message}`
          }));
        }
      }, 2000); // Poll every 2 seconds
      
      // Clean up interval on component unmount
      return () => clearInterval(pollInterval);
    } catch (error: any) {
      console.error('Error setting up polling:', error);
    }
  };

  // Check Drive sync status
  const checkDriveSyncStatus = async () => {
    if (!selectedProject) return;
    
    try {
      const response = await fetch(`http://localhost:8000/api/drive/status/${selectedProject.id}`);
      
      if (response.ok) {
        const data = await response.json();
        
        if (data.is_indexed) {
          setSyncStatus(prev => ({
            ...prev,
            lastSynced: data.last_synced,
            syncedFiles: data.unique_files,
            totalChunks: data.total_chunks,
            // If there's an active sync job, maintain syncing state
            isSyncing: data.sync_in_progress || prev.isSyncing
          }));
        }
      }
    } catch (error) {
      console.error('Error checking Drive sync status:', error);
    }
  };

  // Handle Drive toggle
  const handleDriveToggle = async () => {
    // Get Drive data source
    const driveSource = dataSources.find(source => source.id === 'drive');
    
    if (driveSource) {
      // If not authenticated but toggling ON, trigger OAuth flow first
      if (!driveSource.enabled && !googleAuth.authenticated) {
        handleAuthGoogle();
        return;
      }
      
      // Toggle data source state
      toggleDataSource('drive');
      
      // If toggling ON and authenticated, trigger sync
      if (!driveSource.enabled && googleAuth.authenticated) {
        await syncWithGoogleDrive();
      }
    }
  };

  // Get current project's documents
  const currentProjectDocuments = projectDocuments.filter(
    doc => selectedProject && doc.projectId === selectedProject.id
  );

  // Function to get icon based on document type
  const getDocumentIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="text-red-500">
            <path d="M7 3a1 1 0 011-1h8a1 1 0 011 1v12a1 1 0 01-1 1H8a1 1 0 01-1-1V3z" />
            <path d="M6 12v8a1 1 0 001 1h10a1 1 0 001-1v-8h-2v7H8v-7H6z" />
          </svg>
        );
      case 'document':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="text-blue-500">
            <path d="M7 3a1 1 0 011-1h8a1 1 0 011 1v12a1 1 0 01-1 1H8a1 1 0 01-1-1V3z" />
            <path d="M6 12v8a1 1 0 001 1h10a1 1 0 001-1v-8h-2v7H8v-7H6z" />
          </svg>
        );
      case 'spreadsheet':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="text-green-500">
            <path d="M7 3a1 1 0 011-1h8a1 1 0 011 1v12a1 1 0 01-1 1H8a1 1 0 01-1-1V3z" />
            <path d="M6 12v8a1 1 0 001 1h10a1 1 0 001-1v-8h-2v7H8v-7H6z" />
          </svg>
        );
      case 'presentation':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="text-yellow-500">
            <path d="M7 3a1 1 0 011-1h8a1 1 0 011 1v12a1 1 0 01-1 1H8a1 1 0 01-1-1V3z" />
            <path d="M6 12v8a1 1 0 001 1h10a1 1 0 001-1v-8h-2v7H8v-7H6z" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="text-gray-500">
            <path d="M7 3a1 1 0 011-1h8a1 1 0 011 1v12a1 1 0 01-1 1H8a1 1 0 01-1-1V3z" />
            <path d="M6 12v8a1 1 0 001 1h10a1 1 0 001-1v-8h-2v7H8v-7H6z" />
          </svg>
        );
    }
  };

  // Group data sources by category
  const googleSources = dataSources.filter(source => source.category === 'google');
  const microsoftSources = dataSources.filter(source => source.category === 'microsoft');
  const externalSources = dataSources.filter(source => source.category === 'external');

  // Format the last synced time
  const formatLastSynced = (isoString: string | null): string => {
    if (!isoString) return 'Never';
    
    const date = new Date(isoString);
    return date.toLocaleString();
  };

  return (
    <div className={`fixed top-0 right-0 h-full bg-white shadow-lg transition-all duration-300 z-40 ${isOpen ? 'w-80' : 'w-0'}`}>
      <div className="flex flex-col h-full">
        {/* Toggle Button */}
        <button 
          className="absolute -left-12 top-5 p-2 bg-white shadow-md rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-200"
          onClick={() => setIsOpen(!isOpen)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className={`h-6 w-6 transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
          </svg>
        </button>
        
        {/* Content */}
        {isOpen && (
          <div className="p-6 flex-1 overflow-y-auto hide-scrollbar">
            <div className="flex justify-between items-center mb-5">
              <h2 className="text-xl font-semibold">Data Sources</h2>
              <button 
                onClick={() => setIsOpen(false)}
                className="text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-200 rounded"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
            
            {/* Connected Services */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-medium">Connected Services</h3>
              </div>
              
              <div className="border rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <div className="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center text-red-500 mr-3">
                    <span>G</span>
                  </div>
                  <div>
                    <h4 className="font-medium">Google Workspace</h4>
                    <p className="text-xs text-gray-500">
                      {googleAuth.authenticated ? 'Connected' : 'Not connected'}
                    </p>
                  </div>
                </div>
                
                {/* Google Authentication */}
                <div className="mb-5">
                  <h3 className="text-sm font-semibold mb-2">Google Integration</h3>
                  <div className="bg-white p-3 rounded-lg border border-gray-200 shadow-sm">
                    {googleAuth.authenticated ? (
                      <div>
                        <div className="flex items-center mb-2">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                          <p className="text-sm font-medium">Connected to Google</p>
                        </div>
                        <button 
                          onClick={handleDisconnect}
                          className="text-sm text-red-600 hover:text-red-700 flex items-center"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clipRule="evenodd" />
                          </svg>
                          Disconnect
                        </button>
                      </div>
                    ) : (
                      <div className="flex flex-col">
                        <p className="text-sm text-gray-600 mb-2">Connect your Google account to enable Drive integration.</p>
                        <button 
                          onClick={handleAuthGoogle}
                          className="inline-flex justify-center items-center px-3 py-1.5 text-sm font-medium rounded border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                          <svg className="w-4 h-4 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48">
                            <path fill="#FFC107" d="M43.611 20.083H42V20H24v8h11.303c-1.649 4.657-6.08 8-11.303 8-6.627 0-12-5.373-12-12s5.373-12 12-12c3.059 0 5.842 1.154 7.961 3.039l5.657-5.657C34.046 6.053 29.268 4 24 4 12.955 4 4 12.955 4 24s8.955 20 20 20 20-8.955 20-20c0-1.341-.138-2.65-.389-3.917z" />
                            <path fill="#FF3D00" d="M6.306 14.691l6.571 4.819C14.655 15.108 18.961 12 24 12c3.059 0 5.842 1.154 7.961 3.039l5.657-5.657C34.046 6.053 29.268 4 24 4 16.318 4 9.656 8.337 6.306 14.691z" />
                            <path fill="#4CAF50" d="M24 44c5.166 0 9.86-1.977 13.409-5.192l-6.19-5.238A11.91 11.91 0 0124 36c-5.202 0-9.619-3.317-11.283-7.946l-6.522 5.025C9.505 39.556 16.227 44 24 44z" />
                            <path fill="#1976D2" d="M43.611 20.083H42V20H24v8h11.303a12.04 12.04 0 01-4.087 5.571l.003-.002 6.19 5.238C36.971 39.205 44 34 44 24c0-1.341-.138-2.65-.389-3.917z" />
                          </svg>
                          Connect with Google
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Data Sources */}
                <div className="mb-5">
                  <h3 className="text-sm font-semibold mb-2">Data Sources</h3>
                  
                  {googleAuth.authenticated && (
                    <div className="space-y-3">
                      {googleSources.map(source => (
                        <div key={source.id} className="flex justify-between items-center">
                          <div className="flex-1">
                            <span className="text-sm">{source.name}</span>
                            {source.id === 'drive' && (
                              <div className="text-xs mt-1">
                                {syncStatus.isSyncing ? (
                                  <div className="flex items-center text-blue-600">
                                    <svg className="animate-spin h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    <span>Syncing Drive files...</span>
                                  </div>
                                ) : syncStatus.error ? (
                                  <div className="text-red-500 flex items-start">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mt-0.5 mr-1 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                                    </svg>
                                    <span>{syncStatus.error}</span>
                                  </div>
                                ) : syncStatus.lastSynced ? (
                                  <div className="text-gray-600">
                                    <div className="flex items-center">
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                      </svg>
                                      <span>Last synced: {formatLastSynced(syncStatus.lastSynced)}</span>
                                    </div>
                                    <div className="mt-0.5 flex items-center">
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                                      </svg>
                                      <span>{syncStatus.syncedFiles} files, {syncStatus.totalChunks} chunks</span>
                                    </div>
                                  </div>
                                ) : source.enabled ? (
                                  <div className="text-gray-500 flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 102 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                                    </svg>
                                    <span>Click Sync to import files</span>
                                  </div>
                                ) : null}
                              </div>
                            )}
                          </div>
                          
                          <div className="flex items-center">
                            {source.id === 'drive' && syncStatus.isSyncing && (
                              <div className="mr-2 h-4 w-4">
                                <svg className="animate-spin h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                              </div>
                            )}
                            
                            <ToggleSwitch 
                              enabled={source.enabled} 
                              onChange={source.id === 'drive' ? handleDriveToggle : () => toggleDataSource(source.id)}
                            />
                          </div>
                        </div>
                      ))}
                      
                      {/* Sync status/error messages */}
                      {syncStatus.error && (
                        <div className="text-xs text-red-500 mt-2 p-2 bg-red-50 rounded">
                          {syncStatus.error}
                        </div>
                      )}
                      
                      {/* Manual sync button */}
                      {googleAuth.authenticated && (
                        <button
                          className={`mt-3 text-sm py-1.5 px-3 border rounded flex items-center justify-center w-full transition-all ${
                            syncStatus.isSyncing 
                              ? 'bg-blue-50 border-blue-300 text-blue-500 opacity-70 cursor-not-allowed' 
                              : 'border-blue-300 text-blue-600 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-200'
                          }`}
                          onClick={syncWithGoogleDrive}
                          disabled={syncStatus.isSyncing}
                        >
                          {syncStatus.isSyncing ? (
                            <>
                              <svg className="animate-spin h-4 w-4 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                              </svg>
                              Syncing Google Drive...
                            </>
                          ) : (
                            <>
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                              </svg>
                              Sync Google Drive
                            </>
                          )}
                        </button>
                      )}
                    </div>
                  )}
                </div>
              </div>
              
              <div className="border rounded-lg p-4 mt-3">
                <div className="flex items-center mb-3">
                  <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-500 mr-3">
                    <span>M</span>
                  </div>
                  <div>
                    <h4 className="font-medium">Microsoft 365</h4>
                    <p className="text-xs text-gray-500">Not connected</p>
                  </div>
                </div>
                
                <button className="text-sm text-blue-600 hover:underline">
                  + Connect Microsoft 365
                </button>
              </div>
            </div>
            
            {/* External Sources */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-medium">External Sources</h3>
                <button className="text-blue-600 text-sm hover:underline">Configure</button>
              </div>
              
              <div className="border rounded-lg p-4">
                <div className="space-y-3">
                  {externalSources.map(source => (
                    <div key={source.id} className="flex justify-between items-center">
                      <div>
                        <span className="text-sm">{source.name}</span>
                        {source.id === 'web-search' && (
                          <span className="ml-2 text-xs bg-orange-100 text-orange-600 px-2 py-0.5 rounded-full">
                            Limited
                          </span>
                        )}
                      </div>
                      <ToggleSwitch 
                        enabled={source.enabled} 
                        onChange={() => toggleDataSource(source.id)}
                      />
                    </div>
                  ))}
                </div>
                
                <p className="text-xs text-gray-500 mt-3">
                  Enhanced web search capabilities coming soon
                </p>
              </div>
            </div>
            
            {/* Project Documents */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <h3 className="font-medium">Project Documents</h3>
                <button 
                  className="text-blue-600 text-sm hover:underline"
                  onClick={() => {
                    // Trigger file upload in MainContent component
                    document.getElementById('file-upload-button')?.click();
                  }}
                >
                  Upload
                </button>
              </div>
              
              <div className="border rounded-lg p-4 min-h-[100px] max-h-[300px] overflow-y-auto">
                {currentProjectDocuments.length > 0 ? (
                  <div className="space-y-2">
                    {currentProjectDocuments.map((document) => (
                      <div key={document.id} className="flex items-center py-2 border-b border-gray-100 last:border-0">
                        <div className="w-6 h-6 mr-2 flex-shrink-0">
                          {getDocumentIcon(document.type)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm truncate font-medium">{document.name}</p>
                          <div className="flex items-center">
                            <p className="text-xs text-gray-500 mr-2">{document.date}</p>
                            <p className="text-xs text-gray-400">{document.size}</p>
                          </div>
                        </div>
                        <button className="text-gray-400 hover:text-gray-600">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                          </svg>
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center h-20 text-gray-400">
                    <p className="text-sm">No documents uploaded</p>
                    <p className="text-xs mt-1">Upload files to use in your conversation</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RightSidebar; 