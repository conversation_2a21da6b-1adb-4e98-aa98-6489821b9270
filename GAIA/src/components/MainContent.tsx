import React, { useRef, useEffect, useState } from 'react';
import { useAppContext } from '../context/AppContext';
import { ChatMessage, ChatInput } from './chat';
import { Project, Message } from '../context/AppContext';

// Modal components for Help & Tips and Project Settings
const HelpTipsModal: React.FC<{isOpen: boolean; onClose: () => void}> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 animate-fadeIn">
      <div className="bg-white rounded-lg p-6 w-[600px] max-w-full">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Help & Tips</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="text-gray-700 space-y-4">
          <div>
            <h3 className="font-medium mb-1">Getting Started</h3>
            <p>Welcome to GAIA! Here are a few tips to help you get started:</p>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li>Create a new project for each topic or work area</li>
              <li>Upload relevant documents to provide context</li>
              <li>Ask questions related to your documents</li>
              <li>GAIA will search through your documents to provide relevant answers</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium mb-1">Data Sources</h3>
            <p>GAIA can access various data sources to provide better answers:</p>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li>Google Drive - documents, spreadsheets, and presentations</li>
              <li>Gmail - emails and attachments</li>
              <li>Uploaded files - PDFs, Word documents, etc.</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium mb-1">Commands</h3>
            <p>Try these commands to see what GAIA can do:</p>
            <ul className="list-disc pl-5 mt-2 space-y-1">
              <li>"Summarize the latest report about Q2 sales"</li>
              <li>"Find emails from John about the project timeline"</li>
              <li>"What were the key points from yesterday's meeting?"</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

const ProjectSettingsModal: React.FC<{
  isOpen: boolean; 
  onClose: () => void;
  project: Project | null;
}> = ({ isOpen, onClose, project }) => {
  const [projectName, setProjectName] = useState('');
  const { setSelectedProject, deleteProject } = useAppContext();
  const [isDeleting, setIsDeleting] = useState(false);
  
  useEffect(() => {
    if (project) {
      setProjectName(project.title);
    }
  }, [project]);
  
  if (!isOpen || !project) return null;

  const handleRenameProject = () => {
    if (!projectName.trim() || !project) return;
    
    // Update the project name in context
    const updatedProject = { ...project, title: projectName.trim() };
    setSelectedProject(updatedProject);
    onClose();
  };

  const handleDeleteProject = () => {
    if (!project) return;
    
    setIsDeleting(true);
    // Small delay to show loading state
    setTimeout(() => {
      deleteProject(project.id);
      setIsDeleting(false);
      onClose();
    }, 500);
  };
  
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-96">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Project Settings</h2>
          <button onClick={onClose} className="text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">Project Name</label>
          <input
            type="text"
            className="w-full border border-gray-300 rounded p-2"
            value={projectName}
            onChange={(e) => setProjectName(e.target.value)}
          />
        </div>
        
        <div className="mb-4">
          <button 
            className={`w-full py-2 px-4 border border-red-300 text-red-600 rounded flex items-center justify-center ${
              isDeleting ? 'opacity-70 cursor-not-allowed' : 'hover:bg-red-50'
            }`}
            onClick={handleDeleteProject}
            disabled={isDeleting}
          >
            {isDeleting ? (
              <svg className="animate-spin h-5 w-5 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            )}
            Delete Project
          </button>
        </div>
        
        <div className="flex justify-end space-x-2">
          <button 
            className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50"
            onClick={onClose}
          >
            Cancel
          </button>
          <button 
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            onClick={handleRenameProject}
          >
            Save
          </button>
        </div>
      </div>
    </div>
  );
};

const MainContent: React.FC = () => {
  const { selectedProject, addProjectDocument, setSelectedProject } = useAppContext();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);
  const [isSettingsModalOpen, setIsSettingsModalOpen] = useState(false);
  
  // API base URL
  const API_URL = 'http://localhost:8000/api';

  // Scroll to bottom of messages when they change
  useEffect(() => {
    scrollToBottom();
  }, [selectedProject?.messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleFileUpload = () => {
    fileInputRef.current?.click();
  };

  // Handle file upload from local system
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || !selectedProject) return;
    
    try {
      // Create FormData
      const formData = new FormData();
      
      // Add all files to the FormData
      for (let i = 0; i < e.target.files.length; i++) {
        formData.append('files', e.target.files[i]);
      }
      
      // Add project ID
      formData.append('project_id', selectedProject.id);
      
      // Use Google user ID if authenticated
      const userId = getUserId();
      if (userId) {
        formData.append('user_id', userId);
      }
      
      // Make the API call
      const response = await fetch(`${API_URL}/upload/files`, {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error(`Error uploading files: ${response.statusText}`);
      }
      
      const result = await response.json();
      console.log('Upload result:', result);
      
      // Create a copy of updated project to trigger rerenders
      const updatedProject = { ...selectedProject };
      
      // Add files to the project documents
      for (let i = 0; i < e.target.files.length; i++) {
        const file = e.target.files[i];
        
        // Determine file type
        let fileType: 'document' | 'spreadsheet' | 'pdf' | 'presentation' = 'document';
        
        if (file.name.endsWith('.pdf')) {
          fileType = 'pdf';
        } else if (file.name.endsWith('.xlsx') || file.name.endsWith('.csv') || file.name.endsWith('.xls')) {
          fileType = 'spreadsheet';
        } else if (file.name.endsWith('.pptx') || file.name.endsWith('.ppt')) {
          fileType = 'presentation';
        } else if (file.name.endsWith('.docx') || file.name.endsWith('.doc') || file.name.endsWith('.txt')) {
          fileType = 'document';
        }
        
        // Format file size
        const fileSize = formatFileSize(file.size);
        
        // Create new document
        const newDocument = {
          id: `${Date.now()}-${i}`,
          name: file.name,
          size: fileSize,
          date: new Date().toLocaleDateString(),
          type: fileType,
          projectId: selectedProject.id
        };
        
        // Add document to context
        addProjectDocument(newDocument);
      }
      
      // Update the selected project to trigger rerenders
      setSelectedProject(updatedProject);
      
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error uploading files:', error);
      alert(`Failed to upload files: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
    else return (bytes / 1048576).toFixed(1) + ' MB';
  };

  const getFileType = (filename: string): 'spreadsheet' | 'document' | 'pdf' | 'presentation' => {
    const ext = filename.split('.').pop()?.toLowerCase();
    
    if (ext === 'xlsx' || ext === 'xls' || ext === 'csv') return 'spreadsheet';
    if (ext === 'pdf') return 'pdf';
    if (ext === 'ppt' || ext === 'pptx') return 'presentation';
    return 'document'; // default
  };

  // Check if user is authenticated with Google
  const isGoogleAuthenticated = () => {
    const authData = localStorage.getItem('googleAuth');
    return authData ? JSON.parse(authData).authenticated : false;
  };

  // Get user ID from local storage
  const getUserId = () => {
    const authData = localStorage.getItem('googleAuth');
    return authData ? JSON.parse(authData).userId : null;
  };

  if (!selectedProject) {
    return <div className="flex-1 flex items-center justify-center">No project selected</div>;
  }

  return (
    <div className="flex-1 flex flex-col h-screen overflow-hidden">
      {/* Project Header */}
      <div className="border-b border-gray-200 p-4 bg-white sticky top-0 z-10">
        <div className="flex justify-between items-center">
          <h1 className="text-2xl font-medium">{selectedProject.title}</h1>
          <div className="flex items-center space-x-2">
            <button className="text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-200 rounded">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
              </svg>
            </button>
            <button className="text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-200 rounded" onClick={() => setIsSettingsModalOpen(true)}>
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
        <p className="text-sm text-gray-500">Created on {new Date().toLocaleDateString()} • {selectedProject.messages.length} messages</p>
      </div>

      {/* Project Details */}
      <div className="bg-gray-100 p-6 border-b border-gray-200">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-xl font-medium text-center mb-4">{selectedProject.title} Project</h2>
          <p className="text-center text-gray-700">
            {selectedProject.description}
          </p>
          
          <div className="flex justify-center mt-6 space-x-4">
            <button 
              className="px-4 py-2 bg-white border border-gray-200 rounded-lg shadow-sm flex items-center hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-200"
              onClick={handleFileUpload}
              id="file-upload-button"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
              Upload Files
            </button>
            
            <button 
              className="px-4 py-2 bg-white border border-gray-200 rounded-lg shadow-sm flex items-center hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-200"
              onClick={() => setIsHelpModalOpen(true)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
              </svg>
              Help & Tips
            </button>
            
            <button 
              className="px-4 py-2 bg-white border border-gray-200 rounded-lg shadow-sm flex items-center hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-200"
              onClick={() => setIsSettingsModalOpen(true)}
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-gray-500" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
              </svg>
              Project Settings
            </button>
          </div>
        </div>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 p-4 overflow-y-auto bg-gray-50 hide-scrollbar">
        <div className="max-w-4xl mx-auto space-y-4">
          {selectedProject.messages.length > 0 ? (
            selectedProject.messages.map((message) => (
              <ChatMessage key={message.id} message={message} />
            ))
          ) : (
            <div className="text-center py-10">
              <p className="text-gray-500">No messages yet. Start a conversation!</p>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Chat Input */}
      <div className="p-4 border-t border-gray-200 bg-white sticky bottom-0">
        <div className="max-w-4xl mx-auto">
          <ChatInput />
        </div>
      </div>

      {/* Hidden File Input */}
      <input 
        type="file" 
        className="hidden" 
        ref={fileInputRef} 
        onChange={handleFileChange}
        multiple
        accept=".pdf,.doc,.docx,.txt,.xlsx,.xls,.ppt,.pptx"
      />

      {/* Help Tips Modal */}
      <HelpTipsModal 
        isOpen={isHelpModalOpen} 
        onClose={() => setIsHelpModalOpen(false)} 
      />

      {/* Project Settings Modal */}
      <ProjectSettingsModal 
        isOpen={isSettingsModalOpen}
        onClose={() => setIsSettingsModalOpen(false)}
        project={selectedProject}
      />
    </div>
  );
};

export default MainContent; 