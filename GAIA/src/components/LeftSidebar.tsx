import React, { useState } from 'react';
import { useAppContext } from '../context/AppContext';

// Modal component for creating a new project
const NewProjectModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSave: (projectName: string) => void;
}> = ({ isOpen, onClose, onSave }) => {
  const [projectName, setProjectName] = useState('');

  if (!isOpen) return null;

  const handleSave = () => {
    if (projectName.trim()) {
      onSave(projectName);
      setProjectName('');
      onClose();
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-96">
        <h2 className="text-xl font-semibold mb-4">Create New Project</h2>
        <input
          type="text"
          className="w-full border border-gray-300 rounded p-2 mb-4"
          placeholder="Project name"
          value={projectName}
          onChange={(e) => setProjectName(e.target.value)}
          autoFocus
        />
        <div className="flex justify-end space-x-2">
          <button 
            className="px-4 py-2 border border-gray-300 rounded"
            onClick={onClose}
          >
            Cancel
          </button>
          <button 
            className="px-4 py-2 bg-blue-600 text-white rounded"
            onClick={handleSave}
          >
            Create
          </button>
        </div>
      </div>
    </div>
  );
};

const LeftSidebar: React.FC = () => {
  const { projects, selectedProject, setSelectedProject, createNewProject } = useAppContext();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleNewProject = (projectName: string) => {
    createNewProject(projectName);
  };

  return (
    <div className="w-64 h-screen bg-white border-r border-gray-200 flex flex-col sticky top-0 left-0">
      <div className="p-4 border-b border-gray-200 flex items-center justify-between">
        <h1 className="text-xl font-semibold text-blue-600">GAIA</h1>
        <button 
          className="bg-blue-600 text-white rounded-full h-8 w-8 flex items-center justify-center hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
          onClick={() => setIsModalOpen(true)}
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
        </button>
      </div>
      
      <div className="flex-1 overflow-y-auto hide-scrollbar">
        <div className="py-4">
          <h2 className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-4 mb-2">Projects</h2>
          {projects.map(project => (
            <div 
              key={project.id}
              className={`px-4 py-2 cursor-pointer transition-colors ${
                selectedProject?.id === project.id 
                  ? 'bg-blue-50 text-blue-600 border-r-4 border-blue-600' 
                  : 'text-gray-700 hover:bg-gray-50'
              }`}
              onClick={() => setSelectedProject(project)}
            >
              <h3 className="font-medium truncate">{project.title}</h3>
              <p className="text-xs text-gray-500">{project.timestamp}</p>
            </div>
          ))}
        </div>
      </div>
      
      <div className="p-4 border-t border-gray-200">
        <button 
          onClick={() => setIsModalOpen(true)}
          className="w-full py-2 flex items-center justify-center text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          New Project
        </button>
      </div>
      
      <NewProjectModal 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)} 
        onSave={handleNewProject} 
      />
    </div>
  );
};

export default LeftSidebar; 