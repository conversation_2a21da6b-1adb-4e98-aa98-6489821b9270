import React, { createContext, useState, useContext, ReactNode, useCallback } from 'react';

// Helper function to generate unique IDs
const generateId = () => {
  return Date.now().toString() + '-' + Math.random().toString(36).substr(2, 9);
};

// Define types
export interface Project {
  id: string;
  title: string;
  timestamp: string;
  description: string;
  messages: Message[];
}

export interface Message {
  id: string;
  sender: 'user' | 'ai';
  content: string;
  timestamp: string;
}

interface DataSource {
  id: string;
  name: string;
  enabled: boolean;
  category: 'google' | 'microsoft' | 'external';
}

export interface ProjectDocument {
  id: string;
  name: string;
  size: string;
  date: string;
  type: 'spreadsheet' | 'document' | 'pdf' | 'presentation';
  projectId: string;
}

interface AppContextType {
  selectedProject: Project | null;
  projects: Project[];
  dataSources: DataSource[];
  projectDocuments: ProjectDocument[];
  setSelectedProject: (project: Project) => void;
  toggleDataSource: (id: string) => void;
  sendMessage: (message: string) => Promise<void>;
  isLoading: boolean;
  createNewProject: (name: string) => void;
  addProjectDocument: (document: ProjectDocument) => void;
  deleteProject: (id: string) => void;
}

// Create context
const AppContext = createContext<AppContextType | undefined>(undefined);

// API URL
const API_URL = 'http://localhost:8000/api';

// Default project
const defaultProject: Project = {
  id: 'default',
  title: 'My First Project',
  timestamp: 'just now',
  description: 'Welcome to GAIA. Start by sending your first message!',
  messages: []
};

// Data sources - Google Drive toggles default to OFF for better user control
const initialDataSources: DataSource[] = [
  { id: 'gmail', name: 'Gmail', enabled: false, category: 'google' },
  { id: 'drive', name: 'Drive', enabled: false, category: 'google' },
  { id: 'meet', name: 'Meet', enabled: false, category: 'google' },
  { id: 'chat', name: 'Chat', enabled: false, category: 'google' },
  { id: 'web-search', name: 'Web Search', enabled: false, category: 'external' },
];

// Provider component
export const AppContextProvider: React.FC<{children: ReactNode}> = ({ children }) => {
  const [selectedProject, setSelectedProject] = useState<Project | null>(defaultProject);
  const [projects, setProjects] = useState<Project[]>([defaultProject]);
  const [dataSources, setDataSources] = useState<DataSource[]>(initialDataSources);
  const [projectDocuments, setProjectDocuments] = useState<ProjectDocument[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Create a new project
  const createNewProject = useCallback((name: string) => {
    if (!name.trim()) return;

    const newProject: Project = {
      id: Date.now().toString(),
      title: name,
      timestamp: 'just now',
      description: 'New project created',
      messages: []
    };

    setProjects(prevProjects => [...prevProjects, newProject]);
    setSelectedProject(newProject);
  }, []);

  // Add a document to the current project
  const addProjectDocument = useCallback((document: ProjectDocument) => {
    setProjectDocuments(prevDocs => [...prevDocs, document]);
  }, []);

  const toggleDataSource = useCallback((id: string) => {
    setDataSources(prev =>
      prev.map(source =>
        source.id === id ? { ...source, enabled: !source.enabled } : source
      )
    );
  }, []);

  // Send message to API and handle streaming response
  const sendMessage = async (message: string) => {
    if (!selectedProject) return;

    try {
      setIsLoading(true);

      // Create a new user message
      const userMessage: Message = {
        id: generateId(),
        sender: 'user',
        content: message,
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };

      // Add user message to project
      const updatedProject = {
        ...selectedProject,
        messages: [...selectedProject.messages, userMessage]
      };
      setSelectedProject(updatedProject);

      // Check if Drive is enabled
      const driveSource = dataSources.find(source => source.id === 'drive');
      const useDriveContext = driveSource?.enabled || false;

      // Get Google user ID if authenticated
      let userId = null;
      const storedAuth = localStorage.getItem('googleAuth');
      if (storedAuth) {
        const authData = JSON.parse(storedAuth);
        userId = authData.userId;
      }

      // Prepare API request
      const requestOptions = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message,
          project_id: selectedProject.id,
          use_drive_context: useDriveContext,
          user_id: userId
        })
      };

      // Call the chat API with streaming response
      const response = await fetch(`http://localhost:8000/api/chat/${selectedProject.id}`, requestOptions);

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      // Create a new AI message as a placeholder
      const aiMessage: Message = {
        id: generateId(),
        sender: 'ai',
        content: '',
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };

      // Add AI message to project
      const updatedProjectWithAI = {
        ...updatedProject,
        messages: [...updatedProject.messages, aiMessage]
      };
      setSelectedProject(updatedProjectWithAI);

      // Process streaming response
      const reader = response.body?.getReader();
      if (!reader) throw new Error('Response body is null');

      let done = false;
      let accumulatedContent = '';

      while (!done) {
        const { value, done: readerDone } = await reader.read();
        done = readerDone;

        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.substring(6));

              if (data.is_complete) {
                // Stream completed
                break;
              }

              if (data.is_error) {
                // Handle error
                console.error('Error from API:', data.content);

                // Update AI message with error
                const updatedMessages = updatedProjectWithAI.messages.map(msg =>
                  msg.id === aiMessage.id ? { ...msg, content: data.content } : msg
                );

                setSelectedProject({
                  ...updatedProjectWithAI,
                  messages: updatedMessages
                });

                break;
              }

              if (data.is_chunk) {
                // Accumulate content
                accumulatedContent += data.content;

                // Update AI message with accumulated content
                const updatedMessages = updatedProjectWithAI.messages.map(msg => {
                  if (msg.id === aiMessage.id) {
                    return {
                      ...msg,
                      content: accumulatedContent
                    };
                  }
                  return msg;
                });

                setSelectedProject({
                  ...updatedProjectWithAI,
                  messages: updatedMessages
                });
              }
            } catch (error) {
              console.error('Error parsing chunk:', line, error);
            }
          }
        }
      }

    } catch (error) {
      console.error('Error sending message:', error);

      // Add error message
      const errorMessage: Message = {
        id: generateId(),
        sender: 'ai',
        content: 'Sorry, there was an error processing your request. Please try again.',
        timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
      };

      setSelectedProject({
        ...selectedProject,
        messages: [...selectedProject.messages, errorMessage]
      });

    } finally {
      setIsLoading(false);
    }
  };

  // Delete a project
  const deleteProject = useCallback((id: string) => {
    // Remove the project from the projects list
    setProjects(prevProjects => prevProjects.filter(project => project.id !== id));

    // If the deleted project was selected, select another project
    if (selectedProject && selectedProject.id === id) {
      // Find the first available project or set to null if none
      const firstProject = projects.find(project => project.id !== id) || null;
      setSelectedProject(firstProject);
    }

    // Clean up associated documents
    setProjectDocuments(prevDocs => prevDocs.filter(doc => doc.projectId !== id));
  }, [selectedProject, projects]);

  return (
    <AppContext.Provider
      value={{
        selectedProject,
        projects,
        dataSources,
        projectDocuments,
        setSelectedProject,
        toggleDataSource,
        sendMessage,
        isLoading,
        createNewProject,
        addProjectDocument,
        deleteProject
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

// Custom hook to use the AppContext
export const useAppContext = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppContext must be used within an AppContextProvider');
  }
  return context;
};