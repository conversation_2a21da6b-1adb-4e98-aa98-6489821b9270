import React, { useEffect } from 'react';
import './App.css';
import { AppContextProvider } from './context/AppContext';
import LeftSidebar from './components/LeftSidebar';
import MainContent from './components/MainContent';
import RightSidebar from './components/RightSidebar';

function App() {
  useEffect(() => {
    // Update the document title
    document.title = 'GAIA - GPT AI Assistant';
  }, []);

  return (
    <AppContextProvider>
      <div className="flex min-h-screen bg-white overflow-hidden">
        <LeftSidebar />
        <MainContent />
        <RightSidebar />
      </div>
    </AppContextProvider>
  );
}

export default App;
