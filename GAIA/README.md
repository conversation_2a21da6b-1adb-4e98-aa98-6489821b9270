# GAIA - Enhanced RAG AI Assistant

GAIA is a sophisticated web-based AI assistant with advanced Retrieval-Augmented Generation (RAG) capabilities, designed for enterprise knowledge workers. It seamlessly integrates uploaded files and Google Drive documents using PostgreSQL with pgvector for high-performance vector storage.

## 🚀 Key Features

### Advanced RAG System
- **Dual Data Sources**: Upload files directly + Google Drive integration
- **PostgreSQL + pgvector**: High-performance vector storage and similarity search
- **OpenAI Embeddings**: text-embedding-3-small for semantic understanding
- **Claude 4 Sonnet Integration**: Intelligent response generation with context awareness
- **Automated Monitoring**: Background tasks for Google Drive sync with change detection
- **Incremental Updates**: Only processes new/modified files for efficiency

### Intelligent Processing
- **Advanced Text Chunking**: Semantic-aware splitting with structure preservation
- **Multi-Strategy Retrieval**: Direct embedding search + alternative phrasing + entity-based search
- **Query Analysis**: Intent detection, entity extraction, and complexity assessment
- **Context Optimization**: Smart context selection for <PERSON>'s token limits

### Enterprise Features
- **Real-time Chat**: Streaming responses with RAG-enhanced context
- **Project Management**: Persistent multi-turn conversations
- **Background Processing**: Celery-based task queue for scalable operations
- **Performance Optimization**: Vector indexing, connection pooling, and caching
- **Comprehensive Monitoring**: Analytics, status tracking, and error handling

## Project Structure

```
gaia-assistant/
├── src/                    # Frontend React application
│   ├── components/         # UI components
│   │   ├── chat/          # Chat-specific components
│   │   └── common/        # Reusable components
│   ├── context/           # React context for state management
│   └── ...
├── backend/               # FastAPI backend
│   ├── app/               # API application
│   │   ├── routes/        # API routes
│   │   └── ...
│   └── ...
└── ...
```

## Setup

### Prerequisites

- **Node.js (v16+)** - For frontend development
- **Python (v3.10+)** - For backend services
- **PostgreSQL (v14+)** - With pgvector extension for vector storage
- **Redis (v6+)** - For background task processing
- **OpenAI API Key** - For embeddings (text-embedding-3-small)
- **Anthropic API Key** - For Claude 4 Sonnet integration
- **Google OAuth Credentials** - For Drive integration (optional)

### Frontend Setup

1. Install dependencies:
   ```
   cd gaia-assistant
   npm install
   ```

2. Start the development server:
   ```
   npm start
   ```

### Enhanced Backend Setup

1. **Install Dependencies:**
   ```bash
   cd gaia-assistant/backend
   pip install -r requirements.txt
   ```

2. **Configure Environment:**
   ```bash
   # Copy the example configuration
   cp .env.example .env

   # Edit .env with your API keys and settings
   nano .env
   ```

   **Required Environment Variables:**
   ```bash
   # API Keys
   OPENAI_API_KEY=your_openai_api_key_here
   ANTHROPIC_API_KEY=your_anthropic_api_key_here

   # Database
   DATABASE_URL=postgresql://postgres:postgres@localhost:5432/gaia

   # Redis for background tasks
   REDIS_URL=redis://localhost:6379/0

   # Google OAuth (optional)
   GOOGLE_CLIENT_ID=your_google_client_id.apps.googleusercontent.com
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   ```

3. **Setup PostgreSQL with pgvector:**
   ```bash
   # Install pgvector extension
   # On Ubuntu/Debian:
   sudo apt install postgresql-14-pgvector

   # Create database
   psql -U postgres
   CREATE DATABASE gaia;
   \c gaia
   CREATE EXTENSION vector;
   \q
   ```

4. **Setup Redis:**
   ```bash
   # On Ubuntu/Debian:
   sudo apt install redis-server
   sudo systemctl start redis-server

   # On macOS:
   brew install redis
   brew services start redis
   ```

5. **Run Setup Script:**
   ```bash
   python setup_enhanced_rag.py
   ```

6. **Start the Services:**
   ```bash
   # Terminal 1: FastAPI server
   python run.py

   # Terminal 2: Celery worker
   celery -A app.services.background_tasks worker --loglevel=info

   # Terminal 3: Celery beat (scheduler)
   celery -A app.services.background_tasks beat --loglevel=info
   ```

## 🧠 Enhanced RAG Capabilities

### Intelligent Document Processing
- **Advanced Chunking**: Semantic-aware text splitting that preserves document structure
- **Multi-Format Support**: PDF, DOCX, TXT, Google Docs, Sheets, and more
- **Metadata Preservation**: Maintains file information, timestamps, and source links
- **Incremental Processing**: Only processes new or modified documents

### Smart Retrieval System
- **Multi-Strategy Search**: Combines direct embedding search, alternative phrasing, and entity-based retrieval
- **Query Intelligence**: Analyzes user intent, extracts entities, and determines complexity
- **Context Optimization**: Intelligently selects and organizes relevant information for Claude
- **Source Attribution**: Provides detailed citations with similarity scores and document links

### Google Drive Integration
- **Automated Monitoring**: Background tasks detect file changes every 30 minutes
- **OAuth Authentication**: Secure Google account integration
- **Change Detection**: Only syncs new or modified files for efficiency
- **Comprehensive Support**: Handles Docs, Sheets, PDFs, and other formats

### Claude 4 Sonnet Integration
- **Intelligent Responses**: Context-aware answer generation with advanced reasoning
- **Query Analysis**: Understands user intent and adjusts response complexity
- **Source Integration**: Seamlessly incorporates retrieved information with proper citations
- **Performance Optimization**: Efficient token usage within Claude's context limits

## Usage

1. Open your browser and navigate to `http://localhost:3000`
2. Create a new project or select an existing one
3. Toggle data sources in the right sidebar
4. Upload documents or connect Google Drive
5. Ask questions - GAIA will use your data to enhance responses

## 🔌 API Endpoints

### Enhanced RAG Endpoints
- `POST /api/enhanced-rag/query` - Intelligent query with Claude integration
- `POST /api/enhanced-rag/drive/sync` - Smart Google Drive sync with change detection
- `GET /api/enhanced-rag/drive/status/{project_id}` - Drive sync status and analytics
- `POST /api/enhanced-rag/drive/detect-changes` - Detect Drive changes without syncing
- `GET /api/enhanced-rag/analytics/{project_id}` - Comprehensive RAG analytics

### Core API Endpoints
- `GET /api/health` - Health check endpoint
- `POST /api/chat/{project_id}` - Chat endpoint with RAG-enhanced responses
- `POST /api/upload/files` - Upload and process documents with advanced chunking
- `POST /api/rag/ingest` - Direct RAG ingestion
- `POST /api/rag/query` - Traditional RAG query

### Google Drive Integration
- `GET /api/drive/files` - List Google Drive files
- `GET /api/drive/files/{file_id}` - Get specific file metadata
- `PUT /api/drive/files/{file_id}/indexed` - Mark file as indexed

## 🧪 Testing

Run the comprehensive test suite:

```bash
cd backend
python test_enhanced_rag.py
```

This tests:
- Database connectivity and pgvector functionality
- OpenAI embedding generation
- Advanced text chunking
- RAG storage and retrieval
- Claude 4 Sonnet integration
- Background task system

## 📚 Documentation

- **[Enhanced RAG Guide](backend/ENHANCED_RAG_GUIDE.md)** - Comprehensive implementation guide
- **[API Documentation](http://localhost:8000/docs)** - Interactive API docs (when server is running)
- **[Google Drive Integration](backend/README_DRIVE_INTEGRATION.md)** - Drive setup and usage
- **[PostgreSQL Migration](backend/POSTGRES_MIGRATION.md)** - Database setup guide

## License

MIT
